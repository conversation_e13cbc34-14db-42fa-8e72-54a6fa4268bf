"use client";

import React from "react";
import { useTranslations } from "next-intl";
import { ArrowRight, Heart, Users, Store, Smartphone } from "lucide-react";
import { cn } from "@/lib/utils";
import Image from "next/image";

interface CTASectionProps {
  className?: string;
}

export const CTASection: React.FC<CTASectionProps> = ({ className }) => {
  return (
    <section
      className={cn(
        "py-20 lg:py-32 relative overflow-hidden",
        "bg-gradient-to-br from-locasa-primary-light to-locasa-success-light dark:from-locasa-primary-dark dark:to-locasa-success-dark",
        className
      )}
    >
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div
          className="absolute inset-0"
          style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23ffffff' fill-opacity='0.1' fill-rule='evenodd'%3E%3Cpath d='m0 40l40-40h-40z'/%3E%3Cpath d='m0 40l40-40h-40z'/%3E%3C/g%3E%3C/svg%3E")`,
          }}
        />
      </div>

      {/* Floating Elements */}
      <div className="absolute top-10 left-10 w-20 h-20 bg-locasa-background-light/10 dark:bg-white/10 rounded-full blur-xl animate-pulse"></div>
      <div className="absolute bottom-10 right-10 w-32 h-32 bg-locasa-background-light/10 dark:bg-white/10 rounded-full blur-xl animate-pulse delay-1000"></div>
      <div className="absolute top-1/2 left-1/4 w-16 h-16 bg-locasa-background-light/10 dark:bg-white/10 rounded-full blur-xl animate-pulse delay-500"></div>

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Left Content */}
          <div className="text-center lg:text-left">
            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-locasa-background-light dark:text-white mb-6 leading-tight">
              Ready to Discover
              <span className="block">Your Local Community?</span>
            </h2>

            <p className="text-lg sm:text-xl text-locasa-background-light/90 dark:text-white/90 mb-8 max-w-2xl mx-auto lg:mx-0">
              Join thousands of people who are already supporting local
              businesses and building stronger communities.
            </p>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start mb-8">
              <button
                className={cn(
                  "inline-flex items-center justify-center gap-2 px-8 py-4 rounded-xl font-semibold",
                  "bg-locasa-background-light dark:bg-white text-locasa-primary-light dark:text-locasa-primary-light",
                  "hover:bg-locasa-background-light/90 dark:hover:bg-white/90 hover:shadow-lg",
                  "transform hover:scale-105 transition-all duration-300",
                  "shadow-lg"
                )}
              >
                <Heart className="h-5 w-5" />
                Get Started
                <ArrowRight className="h-5 w-5" />
              </button>

              <button
                className={cn(
                  "inline-flex items-center justify-center gap-2 px-8 py-4 rounded-xl font-semibold",
                  "bg-transparent border-2 border-locasa-background-light dark:border-white text-locasa-background-light dark:text-white",
                  "hover:bg-locasa-background-light dark:hover:bg-white hover:text-locasa-primary-light dark:hover:text-locasa-primary-light",
                  "transform hover:scale-105 transition-all duration-300"
                )}
              >
                <Users className="h-5 w-5" />
                Learn More
              </button>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-3 gap-8 pt-8 border-t border-locasa-background-light/20 dark:border-white/20">
              <div className="text-center lg:text-left">
                <div className="text-2xl sm:text-3xl font-bold text-locasa-background-light dark:text-white">
                  1000+
                </div>
                <div className="text-sm text-locasa-background-light/80 dark:text-white/80">
                  Local Businesses
                </div>
              </div>
              <div className="text-center lg:text-left">
                <div className="text-2xl sm:text-3xl font-bold text-locasa-background-light dark:text-white">
                  50+
                </div>
                <div className="text-sm text-locasa-background-light/80 dark:text-white/80">
                  Cities
                </div>
              </div>
              <div className="text-center lg:text-left">
                <div className="text-2xl sm:text-3xl font-bold text-locasa-background-light dark:text-white">
                  5000+
                </div>
                <div className="text-sm text-locasa-background-light/80 dark:text-white/80">
                  Happy Users
                </div>
              </div>
            </div>
          </div>

          {/* Right Content - App Preview */}
          <div className="relative">
            <div className="relative mx-auto w-80 h-96 lg:w-96 lg:h-[500px]">
              {/* Phone Frame */}
              <div className="absolute inset-0 bg-locasa-background-light/20 dark:bg-white/20 backdrop-blur-sm rounded-[3rem] p-2 shadow-2xl">
                <div className="w-full h-full bg-locasa-background-light dark:bg-locasa-background-dark rounded-[2.5rem] overflow-hidden">
                  {/* Status Bar */}
                  <div className="h-8 bg-locasa-primary-light dark:bg-locasa-primary-dark flex items-center justify-between px-6 text-locasa-background-light dark:text-white text-xs">
                    <span>9:41</span>
                    <span>●●●</span>
                  </div>

                  {/* App Content */}
                  <div className="p-6 space-y-4">
                    <div className="flex items-center gap-3 mb-6">
                      <Image
                        src="/logo.png"
                        alt="Locasa Logo"
                        width={48}
                        height={48}
                        className="rounded-xl"
                      />
                      <div>
                        <h3 className="font-bold text-locasa-text-light dark:text-locasa-text-dark">
                          Locasa
                        </h3>
                        <p className="text-xs text-locasa-secondary-light dark:text-locasa-secondary-dark">
                          Community Platform
                        </p>
                      </div>
                    </div>

                    {/* Mock Content */}
                    <div className="space-y-3">
                      <div className="h-20 bg-gradient-to-r from-locasa-primary-light/20 to-locasa-success-light/20 dark:from-locasa-primary-dark/20 dark:to-locasa-success-dark/20 rounded-xl border border-locasa-border-light/20 dark:border-locasa-border-dark/20"></div>
                      <div className="h-16 bg-gradient-to-r from-locasa-success-light/20 to-locasa-warning-light/20 dark:from-locasa-success-dark/20 dark:to-locasa-warning-dark/20 rounded-xl border border-locasa-border-light/20 dark:border-locasa-border-dark/20"></div>
                      <div className="h-24 bg-gradient-to-r from-locasa-warning-light/20 to-locasa-primary-light/20 dark:from-locasa-warning-dark/20 dark:to-locasa-primary-dark/20 rounded-xl border border-locasa-border-light/20 dark:border-locasa-border-dark/20"></div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Floating Elements */}
              <div className="absolute -top-4 -right-4 w-16 h-16 bg-locasa-background-light/20 dark:bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center shadow-lg animate-bounce">
                <Smartphone className="h-8 w-8 text-locasa-primary-light dark:text-white" />
              </div>

              <div className="absolute -bottom-4 -left-4 w-12 h-12 bg-locasa-background-light/20 dark:bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center shadow-lg animate-pulse">
                <Store className="h-6 w-6 text-locasa-primary-light dark:text-white" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};
