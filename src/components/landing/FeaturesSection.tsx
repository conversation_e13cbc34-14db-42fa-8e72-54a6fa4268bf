"use client";

import React from "react";
import { useTranslations } from "next-intl";
import { Store, Users, MapPin, Heart, Star, Shield } from "lucide-react";
import { cn } from "@/lib/utils";

interface FeaturesSectionProps {
  className?: string;
}

export const FeaturesSection: React.FC<FeaturesSectionProps> = ({
  className,
}) => {
  const features = [
    {
      icon: Store,
      title: "Local Marketplace",
      description:
        "Discover unique products and services from businesses in your neighborhood.",
      color:
        "from-locasa-primary-light to-locasa-success-light dark:from-locasa-primary-dark dark:to-locasa-success-dark",
    },
    {
      icon: Users,
      title: "Community Driven",
      description:
        "Connect with local entrepreneurs and support your community's growth.",
      color:
        "from-locasa-success-light to-locasa-warning-light dark:from-locasa-success-dark dark:to-locasa-warning-dark",
    },
    {
      icon: MapPin,
      title: "Location Based",
      description:
        "Find businesses near you and explore what your area has to offer.",
      color:
        "from-locasa-warning-light to-locasa-error-light dark:from-locasa-warning-dark dark:to-locasa-error-dark",
    },
    {
      icon: Heart,
      title: "Support Local",
      description:
        "Every purchase helps strengthen your local economy and community.",
      color:
        "from-locasa-error-light to-locasa-primary-light dark:from-locasa-error-dark dark:to-locasa-primary-dark",
    },
    {
      icon: Star,
      title: "Quality Assured",
      description:
        "Verified businesses with authentic reviews from real customers.",
      color:
        "from-locasa-info-light to-locasa-success-light dark:from-locasa-info-dark dark:to-locasa-success-dark",
    },
    {
      icon: Shield,
      title: "Secure & Trusted",
      description:
        "Safe transactions with secure payment processing and buyer protection.",
      color:
        "from-locasa-primary-light to-locasa-info-light dark:from-locasa-primary-dark dark:to-locasa-info-dark",
    },
  ];

  return (
    <section
      className={cn(
        "py-20 lg:py-32 bg-locasa-background-light dark:bg-locasa-background-dark relative overflow-hidden",
        className
      )}
    >
      {/* Background Elements */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-0 left-0 w-72 h-72 bg-locasa-primary-light dark:bg-locasa-primary-dark rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 right-0 w-96 h-96 bg-locasa-success-light dark:bg-locasa-success-dark rounded-full blur-3xl"></div>
      </div>

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-locasa-text-light dark:text-locasa-text-dark mb-6">
            Why Choose
            <span className="block text-transparent bg-clip-text bg-gradient-to-r from-locasa-primary-light to-locasa-success-light dark:from-locasa-primary-dark dark:to-locasa-success-dark">
              Locasa?
            </span>
          </h2>
          <p className="text-lg sm:text-xl text-locasa-secondary-light dark:text-locasa-secondary-dark max-w-3xl mx-auto">
            We're more than just a marketplace - we're a platform that brings
            communities together and helps local businesses thrive.
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => {
            const Icon = feature.icon;
            return (
              <div
                key={index}
                className={cn(
                  "group relative p-8 rounded-2xl border border-locasa-border-light/20 dark:border-locasa-border-dark/20",
                  "bg-locasa-input-light/30 dark:bg-locasa-input-dark/30 backdrop-blur-sm",
                  "hover:border-locasa-primary-light/40 dark:hover:border-locasa-primary-dark/40",
                  "transition-all duration-300 hover:transform hover:scale-105",
                  "hover:shadow-lg hover:shadow-locasa-primary-light/10 dark:hover:shadow-locasa-primary-dark/10"
                )}
              >
                {/* Icon */}
                <div
                  className={cn(
                    "w-16 h-16 rounded-xl bg-gradient-to-br mb-6 flex items-center justify-center",
                    "group-hover:scale-110 transition-transform duration-300",
                    feature.color
                  )}
                >
                  <Icon className="h-8 w-8 text-locasa-background-light dark:text-white" />
                </div>

                {/* Content */}
                <h3 className="text-xl font-bold text-locasa-text-light dark:text-locasa-text-dark mb-4">
                  {feature.title}
                </h3>
                <p className="text-locasa-secondary-light dark:text-locasa-secondary-dark leading-relaxed">
                  {feature.description}
                </p>

                {/* Hover Effect */}
                <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-locasa-primary-light/5 to-locasa-success-light/5 dark:from-locasa-primary-dark/5 dark:to-locasa-success-dark/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </div>
            );
          })}
        </div>
      </div>
    </section>
  );
};
