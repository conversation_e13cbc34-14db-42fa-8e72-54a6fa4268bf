"use client";

import React from "react";
import {
  ArrowRight,
  Store,
  Users,
  Heart,
  Star,
  MapPin,
  Smartphone,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { useTranslations } from "next-intl";
import Image from "next/image";

interface HeroSectionProps {
  className?: string;
}

export const HeroSection: React.FC<HeroSectionProps> = ({ className }) => {
  const t = useTranslations("landing.hero");

  return (
    <section
      className={cn(
        "relative overflow-hidden bg-gradient-to-br from-locasa-background-light to-locasa-input-light dark:from-locasa-background-dark dark:to-locasa-input-dark min-h-screen flex items-center",
        className
      )}
    >
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div
          className="absolute inset-0"
          style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000000' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          }}
        />
      </div>

      {/* Floating Elements */}
      <div className="absolute top-20 left-10 w-20 h-20 bg-locasa-primary-light/20 dark:bg-locasa-primary-dark/20 rounded-full blur-xl animate-pulse"></div>
      <div className="absolute top-40 right-20 w-32 h-32 bg-locasa-success-light/20 dark:bg-locasa-success-dark/20 rounded-full blur-xl animate-pulse delay-1000"></div>
      <div className="absolute bottom-20 left-20 w-24 h-24 bg-locasa-warning-light/20 dark:bg-locasa-warning-dark/20 rounded-full blur-xl animate-pulse delay-500"></div>

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Left Content */}
          <div className="text-center lg:text-left">
            <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-locasa-success-light/10 dark:bg-locasa-success-dark/10 text-locasa-success-light dark:text-locasa-success-dark text-sm font-medium mb-6 animate-fade-in">
              <Heart className="h-4 w-4" />
              Supporting Local Communities
            </div>

            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-locasa-text-light dark:text-locasa-text-dark mb-6 leading-tight animate-fade-in-up">
              {t("title")}
            </h1>

            <p className="text-lg sm:text-xl text-locasa-secondary-light dark:text-locasa-secondary-dark mb-8 max-w-2xl mx-auto lg:mx-0 animate-fade-in-up delay-200">
              {t("subtitle")}
            </p>

            {/* Download Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start mb-12 animate-fade-in-up delay-300">
              <a
                href="#"
                className={cn(
                  "inline-flex items-center justify-center gap-3 px-8 py-4 rounded-xl font-semibold",
                  "text-locasa-background-light dark:text-locasa-text-dark",
                  "bg-gradient-to-r from-locasa-primary-light to-locasa-success-light dark:from-locasa-primary-dark dark:to-locasa-success-dark",
                  "hover:shadow-lg hover:shadow-locasa-primary-light/25 dark:hover:shadow-locasa-primary-dark/25",
                  "transform hover:scale-105 transition-all duration-300",
                  "shadow-lg"
                )}
              >
                <div className="flex items-center justify-center w-6 h-6 bg-locasa-background-light/20 dark:bg-locasa-text-dark/20 rounded">
                  <Smartphone className="h-4 w-4" />
                </div>
                {t("downloadAndroid")}
                <ArrowRight className="h-4 w-4" />
              </a>

              <a
                href="#"
                className={cn(
                  "inline-flex items-center justify-center gap-3 px-8 py-4 rounded-xl font-semibold",
                  "bg-transparent border-2 border-locasa-primary-light dark:border-locasa-primary-dark",
                  "text-locasa-primary-light dark:text-locasa-primary-dark",
                  "hover:bg-locasa-primary-light dark:hover:bg-locasa-primary-dark",
                  "hover:text-locasa-background-light dark:hover:text-locasa-text-dark",
                  "transform hover:scale-105 transition-all duration-300"
                )}
              >
                <div className="flex items-center justify-center w-6 h-6 bg-locasa-primary-light/20 dark:bg-locasa-primary-dark/20 rounded">
                  <Smartphone className="h-4 w-4" />
                </div>
                {t("downloadIOS")}
                <ArrowRight className="h-4 w-4" />
              </a>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-3 gap-8 pt-8 border-t border-locasa-border-light/20 dark:border-locasa-border-dark/20 animate-fade-in-up delay-500">
              <div className="text-center lg:text-left">
                <div className="text-2xl sm:text-3xl font-bold text-locasa-text-light dark:text-locasa-text-dark">
                  1000+
                </div>
                <div className="text-sm text-locasa-secondary-light dark:text-locasa-secondary-dark">
                  Local Businesses
                </div>
              </div>
              <div className="text-center lg:text-left">
                <div className="text-2xl sm:text-3xl font-bold text-locasa-text-light dark:text-locasa-text-dark">
                  5000+
                </div>
                <div className="text-sm text-locasa-secondary-light dark:text-locasa-secondary-dark">
                  Happy Customers
                </div>
              </div>
              <div className="text-center lg:text-left">
                <div className="text-2xl sm:text-3xl font-bold text-locasa-text-light dark:text-locasa-text-dark">
                  50+
                </div>
                <div className="text-sm text-locasa-secondary-light dark:text-locasa-secondary-dark">
                  Cities
                </div>
              </div>
            </div>
          </div>

          {/* Right Content - Beautiful Visual */}
          <div className="relative animate-fade-in-up delay-400">
            <div className="relative mx-auto w-80 h-96 lg:w-96 lg:h-[500px]">
              {/* Main Card */}
              <div className="absolute inset-0 bg-gradient-to-br from-locasa-primary-light to-locasa-success-light dark:from-locasa-primary-dark dark:to-locasa-success-dark rounded-[3rem] p-1 shadow-2xl">
                <div className="w-full h-full bg-locasa-background-light dark:bg-locasa-background-dark rounded-[2.8rem] overflow-hidden">
                  {/* Header */}
                  <div className="h-16 bg-gradient-to-r from-locasa-primary-light to-locasa-success-light dark:from-locasa-primary-dark dark:to-locasa-success-dark flex items-center justify-center text-locasa-background-light dark:text-white">
                    <div className="flex items-center gap-3">
                      <Image
                        src="/logo.png"
                        alt="Locasa Logo"
                        width={32}
                        height={32}
                        className="rounded-lg"
                      />
                      <span className="text-lg font-bold">Locasa</span>
                    </div>
                  </div>

                  {/* Content */}
                  <div className="p-6 space-y-4">
                    {/* Featured Store Card */}
                    <div className="bg-locasa-input-light dark:bg-locasa-input-dark rounded-xl p-4 border border-locasa-border-light/20 dark:border-locasa-border-dark/20">
                      <div className="flex items-center gap-3 mb-3">
                        <div className="w-10 h-10 bg-locasa-success-light dark:bg-locasa-success-dark rounded-lg flex items-center justify-center">
                          <Store className="h-5 w-5 text-locasa-background-light dark:text-white" />
                        </div>
                        <div>
                          <h4 className="font-semibold text-locasa-text-light dark:text-locasa-text-dark text-sm">
                            Local Bakery
                          </h4>
                          <div className="flex items-center gap-1">
                            <Star className="h-3 w-3 text-locasa-warning-light dark:text-locasa-warning-dark fill-current" />
                            <span className="text-xs text-locasa-secondary-light dark:text-locasa-secondary-dark">
                              4.9
                            </span>
                          </div>
                        </div>
                      </div>
                      <div className="h-16 bg-locasa-background-light dark:bg-locasa-background-dark rounded-lg"></div>
                    </div>

                    {/* Quick Stats */}
                    <div className="grid grid-cols-2 gap-3">
                      <div className="bg-locasa-input-light dark:bg-locasa-input-dark rounded-lg p-3 text-center">
                        <div className="text-lg font-bold text-locasa-text-light dark:text-locasa-text-dark">
                          50+
                        </div>
                        <div className="text-xs text-locasa-secondary-light dark:text-locasa-secondary-dark">
                          Stores
                        </div>
                      </div>
                      <div className="bg-locasa-input-light dark:bg-locasa-input-dark rounded-lg p-3 text-center">
                        <div className="text-lg font-bold text-locasa-text-light dark:text-locasa-text-dark">
                          2.5km
                        </div>
                        <div className="text-xs text-locasa-secondary-light dark:text-locasa-secondary-dark">
                          Nearby
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Floating Elements */}
              <div className="absolute -top-4 -right-4 w-16 h-16 bg-locasa-success-light dark:bg-locasa-success-dark rounded-full flex items-center justify-center shadow-lg animate-bounce">
                <Heart className="h-8 w-8 text-locasa-background-light dark:text-white" />
              </div>

              <div className="absolute -bottom-4 -left-4 w-12 h-12 bg-locasa-warning-light dark:bg-locasa-warning-dark rounded-full flex items-center justify-center shadow-lg animate-pulse">
                <MapPin className="h-6 w-6 text-locasa-background-light dark:text-white" />
              </div>

              <div className="absolute top-1/2 -left-8 w-8 h-8 bg-locasa-primary-light dark:bg-locasa-primary-dark rounded-full flex items-center justify-center shadow-lg animate-ping">
                <Users className="h-4 w-4 text-locasa-background-light dark:text-white" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};
