"use client";

import React from "react";
import Image from "next/image";

export const LoadingScreen: React.FC = () => {
  return (
    <div className="fixed inset-0 bg-locasa-background-light dark:bg-locasa-background-dark flex items-center justify-center z-50">
      <div className="flex flex-col items-center gap-4">
        <div className="relative">
          <Image
            src="/logo.png"
            alt="Locasa Logo"
            width={80}
            height={80}
            className="rounded-2xl animate-pulse"
          />
        </div>
        <div className="flex items-center gap-2">
          <div className="w-2 h-2 bg-locasa-primary-light dark:bg-locasa-primary-dark rounded-full animate-bounce"></div>
          <div
            className="w-2 h-2 bg-locasa-success-light dark:bg-locasa-success-dark rounded-full animate-bounce"
            style={{ animationDelay: "0.1s" }}
          ></div>
          <div
            className="w-2 h-2 bg-locasa-warning-light dark:bg-locasa-warning-dark rounded-full animate-bounce"
            style={{ animationDelay: "0.2s" }}
          ></div>
        </div>
      </div>
    </div>
  );
};
