"use client";

import React, { useState, useTransition } from "react";
import { Globe, ChevronDown } from "lucide-react";
import { cn } from "@/lib/utils";
import { useLocale } from "next-intl";
import { setUserLocale } from "@/lib/locale";

const languages = [
  { code: "en", name: "English", flag: "🇺🇸" },
  { code: "fr", name: "Français", flag: "🇫🇷" },
];

interface LanguageSwitcherProps {
  className?: string;
}

export const LanguageSwitcher: React.FC<LanguageSwitcherProps> = ({
  className,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isPending, startTransition] = useTransition();
  const locale = useLocale();

  const currentLanguage =
    languages.find((lang) => lang.code === locale) || languages[0];

  const handleLanguageChange = (langCode: string) => {
    startTransition(() => {
      setUserLocale(langCode as "en" | "fr");
    });
    setIsOpen(false);
  };

  return (
    <div className={cn("relative", className)}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={cn(
          "flex items-center gap-2 px-3 py-2 rounded-lg",
          "bg-locasa-background-light dark:bg-locasa-background-dark",
          "border border-locasa-border-light dark:border-locasa-border-dark",
          "text-locasa-text-light dark:text-locasa-text-dark",
          "hover:bg-locasa-input-light dark:hover:bg-locasa-input-dark",
          "transition-all duration-200 ease-in-out",
          "focus:outline-none focus:ring-2 focus:ring-locasa-primary-light dark:focus:ring-locasa-primary-dark"
        )}
        aria-label="Select language"
      >
        <Globe className="h-4 w-4" />
        <span className="text-sm font-medium">{currentLanguage.flag}</span>
        <ChevronDown
          className={cn(
            "h-4 w-4 transition-transform duration-200",
            isOpen && "rotate-180"
          )}
        />
      </button>

      {isOpen && (
        <>
          {/* Backdrop */}
          <div
            className="fixed inset-0 z-10"
            onClick={() => setIsOpen(false)}
          />

          {/* Dropdown */}
          <div
            className={cn(
              "absolute right-0 top-full mt-2 z-20",
              "min-w-[160px] rounded-lg shadow-lg",
              "bg-locasa-background-light dark:bg-locasa-background-dark",
              "border border-locasa-border-light dark:border-locasa-border-dark",
              "py-1"
            )}
          >
            {languages.map((language) => (
              <button
                key={language.code}
                onClick={() => handleLanguageChange(language.code)}
                className={cn(
                  "w-full flex items-center gap-3 px-4 py-2 text-left",
                  "text-locasa-text-light dark:text-locasa-text-dark",
                  "hover:bg-locasa-input-light dark:hover:bg-locasa-input-dark",
                  "transition-colors duration-150",
                  locale === language.code &&
                    "bg-locasa-primary-light/10 dark:bg-locasa-primary-dark/10"
                )}
              >
                <span className="text-lg">{language.flag}</span>
                <span className="text-sm font-medium">{language.name}</span>
                {locale === language.code && (
                  <div className="ml-auto w-2 h-2 rounded-full bg-locasa-success-light dark:bg-locasa-success-dark" />
                )}
              </button>
            ))}
          </div>
        </>
      )}
    </div>
  );
};
