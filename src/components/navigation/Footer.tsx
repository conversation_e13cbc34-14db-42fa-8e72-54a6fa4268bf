"use client";

import React from "react";
import Link from "next/link";
import { useTranslations } from "next-intl";
import {
  Store,
  Mail,
  Phone,
  MapPin,
  Facebook,
  Twitter,
  Instagram,
  Linkedin,
} from "lucide-react";
import { cn } from "@/lib/utils";

interface FooterProps {
  className?: string;
}

export const Footer: React.FC<FooterProps> = ({ className }) => {
  const t = useTranslations("footer");

  const footerLinks = {
    company: [
      { href: "/about", label: t("aboutUs") },
      { href: "/contact", label: t("contactUs") },
    ],
    support: [
      { href: "/help", label: "Help Center" },
      { href: "/privacy", label: t("privacyPolicy") },
      { href: "/terms", label: t("termsOfService") },
      { href: "/data-deletion", label: "Delete My Data" },
      { href: "/faq", label: "FAQ" },
    ],
  };

  const socialLinks = [
    { href: "#", icon: Facebook, label: "Facebook" },
    { href: "#", icon: Twitter, label: "Twitter" },
    { href: "#", icon: Instagram, label: "Instagram" },
    { href: "#", icon: Linkedin, label: "LinkedIn" },
  ];

  return (
    <footer
      className={cn(
        "bg-locasa-primary-light dark:bg-locasa-primary-dark text-locasa-background-light dark:text-white",
        className
      )}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Main Footer Content */}
        <div className="py-12 lg:py-16">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {/* Brand Section */}
            <div className="lg:col-span-2">
              <Link href="/" className="flex items-center gap-2 mb-4">
                <div className="w-8 h-8 bg-locasa-background-light dark:bg-locasa-background-dark rounded-lg flex items-center justify-center">
                  <Store className="h-5 w-5 text-locasa-primary-light" />
                </div>
                <span className="text-xl font-bold">Locasa</span>
              </Link>
              <p className="text-locasa-background-light/80 dark:text-white/80 mb-6 max-w-sm">
                Your marketplace for authentic local products and services.
                Building stronger communities one purchase at a time.
              </p>

              {/* Contact Info */}
              <div className="space-y-2 text-sm text-locasa-background-light/80 dark:text-white/80">
                <div className="flex items-center gap-2">
                  <Mail className="h-4 w-4" />
                  <span><EMAIL></span>
                </div>
                <div className="flex items-center gap-2">
                  <Phone className="h-4 w-4" />
                  <span>+****************</span>
                </div>
                <div className="flex items-center gap-2">
                  <MapPin className="h-4 w-4" />
                  <span>123 Local Street, Community City</span>
                </div>
              </div>
            </div>

            {/* Company Links */}
            <div>
              <h3 className="font-semibold mb-4">Company</h3>
              <ul className="space-y-2">
                {footerLinks.company.map((link) => (
                  <li key={link.href}>
                    <Link
                      href={link.href}
                      className="text-locasa-background-light/80 dark:text-white/80 hover:text-locasa-background-light dark:hover:text-white transition-colors text-sm"
                    >
                      {link.label}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            {/* Support Links */}
            <div>
              <h3 className="font-semibold mb-4">Support</h3>
              <ul className="space-y-2">
                {footerLinks.support.map((link) => (
                  <li key={link.href}>
                    <Link
                      href={link.href}
                      className="text-locasa-background-light/80 dark:text-white/80 hover:text-locasa-background-light dark:hover:text-white transition-colors text-sm"
                    >
                      {link.label}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>

        {/* Newsletter Section */}
        <div className="py-8 border-t border-locasa-background-light/20 dark:border-white/20">
          <div className="grid md:grid-cols-2 gap-8 items-center">
            <div>
              <h3 className="text-lg font-semibold mb-2">Stay Updated</h3>
              <p className="text-locasa-background-light/80 dark:text-white/80 text-sm">
                Get the latest news about local vendors and special offers
                delivered to your inbox.
              </p>
            </div>
            <div className="flex gap-2">
              <input
                type="email"
                placeholder="Enter your email"
                className="flex-1 px-4 py-2 rounded-lg bg-locasa-background-light/10 dark:bg-white/10 border border-locasa-background-light/20 dark:border-white/20 text-locasa-background-light dark:text-white placeholder-locasa-primary-light/70 dark:placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-locasa-background-light/30 dark:focus:ring-white/30"
              />
              <button className="px-6 py-2 bg-locasa-background-light dark:bg-locasa-background-dark text-locasa-primary-light dark:text-locasa-primary-dark rounded-lg hover:bg-locasa-background-light/90 dark:hover:bg-locasa-background-dark/90 transition-colors font-medium">
                Subscribe
              </button>
            </div>
          </div>
        </div>

        {/* Bottom Footer */}
        <div className="py-6 border-t border-locasa-background-light/20 dark:border-white/20">
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            <div className="text-sm text-locasa-background-light/80 dark:text-white/80">
              © 2024 Locasa. {t("allRightsReserved")}.
            </div>

            {/* Social Links */}
            <div className="flex items-center gap-4">
              <span className="text-sm text-locasa-background-light/80 dark:text-white/80 mr-2">
                {t("followUs")}:
              </span>
              {socialLinks.map((social) => (
                <a
                  key={social.label}
                  href={social.href}
                  className="w-8 h-8 bg-white/10 rounded-lg flex items-center justify-center hover:bg-white/20 transition-colors"
                  aria-label={social.label}
                >
                  <social.icon className="h-4 w-4" />
                </a>
              ))}
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};
