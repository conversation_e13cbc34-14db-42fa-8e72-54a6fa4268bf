"use client";

import React from "react";
import {
  FileText,
  Scale,
  Users,
  ShoppingBag,
  CreditCard,
  Shield,
  AlertTriangle,
  Calendar,
} from "lucide-react";
import { cn } from "@/lib/utils";

export default function TermsOfService() {
  const sections = [
    {
      id: "acceptance",
      title: "Acceptance of Terms",
      icon: FileText,
      content: [
        "By accessing and using the Locasa platform, you accept and agree to be bound by these Terms of Service.",
        "If you do not agree to these terms, you may not use our services.",
        "We reserve the right to modify these terms at any time with notice to users.",
        "Continued use of the platform after changes constitutes acceptance of new terms.",
      ],
    },
    {
      id: "user-accounts",
      title: "User Accounts",
      icon: Users,
      content: [
        "You must be at least 18 years old to create an account on Locasa.",
        "You are responsible for maintaining the confidentiality of your account credentials.",
        "You agree to provide accurate and complete information when creating your account.",
        "You are responsible for all activities that occur under your account.",
        "You must notify us immediately of any unauthorized use of your account.",
      ],
    },
    {
      id: "marketplace-rules",
      title: "Marketplace Rules",
      icon: ShoppingBag,
      content: [
        "All products and services must comply with local laws and regulations.",
        "Vendors are responsible for the accuracy of their product descriptions and pricing.",
        "Prohibited items include illegal goods, counterfeit products, and harmful substances.",
        "We reserve the right to remove listings that violate our policies.",
        "Users must not engage in fraudulent or deceptive practices.",
      ],
    },
    {
      id: "payments",
      title: "Payments and Fees",
      icon: CreditCard,
      content: [
        "All transactions are processed through secure, third-party payment processors.",
        "Vendors are responsible for applicable taxes on their sales.",
        "Platform fees may apply to transactions and will be clearly disclosed.",
        "Refunds are subject to individual vendor policies and our dispute resolution process.",
        "We are not responsible for payment disputes between users and vendors.",
      ],
    },
    {
      id: "intellectual-property",
      title: "Intellectual Property",
      icon: Shield,
      content: [
        "Users retain ownership of content they upload to the platform.",
        "By uploading content, you grant Locasa a license to use it for platform operations.",
        "You must not infringe on the intellectual property rights of others.",
        "We will respond to valid copyright infringement notices under applicable law.",
        "The Locasa brand, logo, and platform design are our intellectual property.",
      ],
    },
    {
      id: "liability",
      title: "Limitation of Liability",
      icon: Scale,
      content: [
        "Locasa acts as a platform connecting buyers and sellers; we are not a party to transactions.",
        "We do not guarantee the quality, safety, or legality of products or services listed.",
        "Our liability is limited to the maximum extent permitted by law.",
        "Users agree to indemnify Locasa against claims arising from their use of the platform.",
        "We are not liable for indirect, incidental, or consequential damages.",
      ],
    },
  ];

  const importantNotices = [
    {
      title: "Dispute Resolution",
      description:
        "Disputes between users should first be resolved directly. We provide mediation services when needed.",
    },
    {
      title: "Account Termination",
      description:
        "We may suspend or terminate accounts that violate these terms or engage in harmful activities.",
    },
    {
      title: "Platform Availability",
      description:
        "We strive for 99.9% uptime but cannot guarantee uninterrupted service availability.",
    },
    {
      title: "Data Protection",
      description:
        "Your personal data is protected according to our Privacy Policy and applicable data protection laws.",
    },
  ];

  return (
    <div className="min-h-screen bg-locasa-background-light dark:bg-locasa-background-dark">
      {/* Hero Section */}
      <section className="py-20 lg:py-32 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-locasa-primary-light/5 to-locasa-info-light/5 dark:from-locasa-primary-dark/5 dark:to-locasa-info-dark/5"></div>

        <div className="relative max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-locasa-info-light/10 dark:bg-locasa-info-dark/10 text-locasa-info-light dark:text-locasa-info-dark text-sm font-medium mb-6">
            <Scale className="h-4 w-4" />
            Legal Terms
          </div>

          <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-locasa-text-light dark:text-locasa-text-dark mb-6 leading-tight">
            Terms of Service
          </h1>

          <p className="text-lg sm:text-xl text-locasa-secondary-light dark:text-locasa-secondary-dark mb-8 max-w-3xl mx-auto">
            These terms govern your use of the Locasa platform. Please read them
            carefully as they contain important information about your rights
            and obligations.
          </p>

          <div className="flex items-center justify-center gap-2 text-sm text-locasa-secondary-light dark:text-locasa-secondary-dark">
            <Calendar className="h-4 w-4" />
            <span>Last updated: December 2024</span>
          </div>
        </div>
      </section>

      {/* Important Notice */}
      <section className="py-8">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="p-6 bg-locasa-warning-light/10 dark:bg-locasa-warning-dark/10 border border-locasa-warning-light/20 dark:border-locasa-warning-dark/20 rounded-2xl">
            <div className="flex items-start gap-4">
              <AlertTriangle className="h-6 w-6 text-locasa-warning-light dark:text-locasa-warning-dark flex-shrink-0 mt-1" />
              <div>
                <h3 className="font-bold text-locasa-text-light dark:text-locasa-text-dark mb-2">
                  Important Legal Agreement
                </h3>
                <p className="text-locasa-secondary-light dark:text-locasa-secondary-dark text-sm leading-relaxed">
                  By using Locasa, you agree to these terms. If you don't agree
                  with any part of these terms, you should not use our platform.
                  These terms may be updated from time to time, and continued
                  use constitutes acceptance of any changes.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Terms Sections */}
      <section className="py-16">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="space-y-8">
            {sections.map((section, index) => {
              const Icon = section.icon;
              return (
                <div
                  key={section.id}
                  className={cn(
                    "p-8 rounded-2xl border border-locasa-border-light/20 dark:border-locasa-border-dark/20",
                    "bg-locasa-input-light/30 dark:bg-locasa-input-dark/30 backdrop-blur-sm"
                  )}
                >
                  <div className="flex items-center gap-4 mb-6">
                    <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-locasa-primary-light to-locasa-success-light dark:from-locasa-primary-dark dark:to-locasa-success-dark flex items-center justify-center">
                      <Icon className="h-6 w-6 text-locasa-background-light dark:text-white" />
                    </div>
                    <h3 className="text-2xl font-bold text-locasa-text-light dark:text-locasa-text-dark">
                      {section.title}
                    </h3>
                  </div>

                  <ul className="space-y-4">
                    {section.content.map((item, itemIndex) => (
                      <li key={itemIndex} className="flex items-start gap-3">
                        <div className="w-2 h-2 rounded-full bg-locasa-primary-light dark:bg-locasa-primary-dark mt-2 flex-shrink-0"></div>
                        <span className="text-locasa-secondary-light dark:text-locasa-secondary-dark leading-relaxed">
                          {item}
                        </span>
                      </li>
                    ))}
                  </ul>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Important Notices */}
      <section className="py-16">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-locasa-text-light dark:text-locasa-text-dark text-center mb-12">
            Important Notices
          </h2>

          <div className="grid md:grid-cols-2 gap-6">
            {importantNotices.map((notice, index) => (
              <div
                key={index}
                className="p-6 bg-locasa-input-light/30 dark:bg-locasa-input-dark/30 rounded-2xl border border-locasa-border-light/20 dark:border-locasa-border-dark/20"
              >
                <h4 className="font-bold text-locasa-text-light dark:text-locasa-text-dark mb-3">
                  {notice.title}
                </h4>
                <p className="text-locasa-secondary-light dark:text-locasa-secondary-dark text-sm leading-relaxed">
                  {notice.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Governing Law */}
      <section className="py-16">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="p-8 bg-gradient-to-br from-locasa-primary-light/10 to-locasa-info-light/10 dark:from-locasa-primary-dark/10 dark:to-locasa-info-dark/10 rounded-2xl border border-locasa-border-light/20 dark:border-locasa-border-dark/20">
            <div className="text-center">
              <Scale className="h-12 w-12 text-locasa-primary-light dark:text-locasa-primary-dark mx-auto mb-4" />
              <h3 className="text-2xl font-bold text-locasa-text-light dark:text-locasa-text-dark mb-4">
                Governing Law
              </h3>
              <p className="text-locasa-secondary-light dark:text-locasa-secondary-dark mb-6 max-w-2xl mx-auto">
                These Terms of Service are governed by and construed in
                accordance with the laws of the jurisdiction where Locasa
                operates. Any disputes arising from these terms will be resolved
                through binding arbitration or in the courts of competent
                jurisdiction.
              </p>

              <div className="grid md:grid-cols-3 gap-4 text-sm">
                <div className="p-4 bg-locasa-background-light/50 dark:bg-locasa-background-dark/50 rounded-lg">
                  <div className="font-semibold text-locasa-text-light dark:text-locasa-text-dark mb-1">
                    Jurisdiction
                  </div>
                  <div className="text-locasa-secondary-light dark:text-locasa-secondary-dark">
                    Local Laws Apply
                  </div>
                </div>
                <div className="p-4 bg-locasa-background-light/50 dark:bg-locasa-background-dark/50 rounded-lg">
                  <div className="font-semibold text-locasa-text-light dark:text-locasa-text-dark mb-1">
                    Dispute Resolution
                  </div>
                  <div className="text-locasa-secondary-light dark:text-locasa-secondary-dark">
                    Arbitration First
                  </div>
                </div>
                <div className="p-4 bg-locasa-background-light/50 dark:bg-locasa-background-dark/50 rounded-lg">
                  <div className="font-semibold text-locasa-text-light dark:text-locasa-text-dark mb-1">
                    Severability
                  </div>
                  <div className="text-locasa-secondary-light dark:text-locasa-secondary-dark">
                    Terms Remain Valid
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-16">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="p-8 bg-locasa-input-light/30 dark:bg-locasa-input-dark/30 rounded-2xl border border-locasa-border-light/20 dark:border-locasa-border-dark/20">
            <FileText className="h-12 w-12 text-locasa-primary-light dark:text-locasa-primary-dark mx-auto mb-4" />
            <h3 className="text-2xl font-bold text-locasa-text-light dark:text-locasa-text-dark mb-4">
              Questions About These Terms?
            </h3>
            <p className="text-locasa-secondary-light dark:text-locasa-secondary-dark mb-6">
              If you have any questions about these Terms of Service or need
              clarification on any provisions, please contact our legal team.
            </p>
            <a
              href="mailto:<EMAIL>"
              className="inline-flex items-center gap-2 px-6 py-3 bg-locasa-primary-light dark:bg-locasa-primary-dark text-locasa-background-light dark:text-white rounded-lg hover:bg-locasa-primary-light/90 dark:hover:bg-locasa-primary-dark/90 transition-colors font-medium"
            >
              <FileText className="h-4 w-4" />
              <EMAIL>
            </a>
          </div>
        </div>
      </section>
    </div>
  );
}
