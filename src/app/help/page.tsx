"use client";

import React, { useState } from "react";
import {
  Search,
  HelpCircle,
  MessageCircle,
  Mail,
  Phone,
  Book,
  Users,
  ShoppingBag,
  CreditCard,
  Shield,
  Settings,
} from "lucide-react";
import { cn } from "@/lib/utils";
import Link from "next/link";

export default function HelpCenter() {
  const [searchQuery, setSearchQuery] = useState("");

  const helpCategories = [
    {
      id: "getting-started",
      title: "Getting Started",
      description: "Learn the basics of using Locasa",
      icon: Book,
      color:
        "from-locasa-primary-light to-locasa-success-light dark:from-locasa-primary-dark dark:to-locasa-success-dark",
      articles: [
        "How to create an account",
        "Setting up your profile",
        "Finding local businesses",
        "Understanding the marketplace",
      ],
    },
    {
      id: "buying",
      title: "Buying & Orders",
      description: "Everything about purchasing and orders",
      icon: ShoppingBag,
      color:
        "from-locasa-success-light to-locasa-warning-light dark:from-locasa-success-dark dark:to-locasa-warning-dark",
      articles: [
        "How to place an order",
        "Payment methods",
        "Order tracking",
        "Returns and refunds",
      ],
    },
    {
      id: "selling",
      title: "Selling & Vendors",
      description: "Guide for local business owners",
      icon: Users,
      color:
        "from-locasa-warning-light to-locasa-error-light dark:from-locasa-warning-dark dark:to-locasa-error-dark",
      articles: [
        "Becoming a vendor",
        "Setting up your store",
        "Managing inventory",
        "Processing orders",
      ],
    },
    {
      id: "payments",
      title: "Payments & Billing",
      description: "Payment processing and billing help",
      icon: CreditCard,
      color:
        "from-locasa-error-light to-locasa-info-light dark:from-locasa-error-dark dark:to-locasa-info-dark",
      articles: [
        "Payment methods accepted",
        "Payment security",
        "Billing issues",
        "Refund process",
      ],
    },
    {
      id: "account",
      title: "Account & Settings",
      description: "Manage your account and preferences",
      icon: Settings,
      color:
        "from-locasa-info-light to-locasa-primary-light dark:from-locasa-info-dark dark:to-locasa-primary-dark",
      articles: [
        "Account settings",
        "Privacy controls",
        "Notification preferences",
        "Deleting your account",
      ],
    },
    {
      id: "security",
      title: "Security & Privacy",
      description: "Keep your account safe and secure",
      icon: Shield,
      color:
        "from-locasa-primary-light to-locasa-success-light dark:from-locasa-primary-dark dark:to-locasa-success-dark",
      articles: [
        "Account security tips",
        "Privacy settings",
        "Reporting issues",
        "Data protection",
      ],
    },
  ];

  const quickActions = [
    {
      title: "Contact Support",
      description: "Get help from our support team",
      icon: MessageCircle,
      href: "/contact",
      color: "bg-locasa-primary-light dark:bg-locasa-primary-dark",
    },
    {
      title: "FAQ",
      description: "Find answers to common questions",
      icon: HelpCircle,
      href: "/faq",
      color: "bg-locasa-success-light dark:bg-locasa-success-dark",
    },
    {
      title: "Email Us",
      description: "Send us an email for detailed help",
      icon: Mail,
      href: "mailto:<EMAIL>",
      color: "bg-locasa-warning-light dark:bg-locasa-warning-dark",
    },
    {
      title: "Call Us",
      description: "Speak with our support team",
      icon: Phone,
      href: "tel:+***********",
      color: "bg-locasa-info-light dark:bg-locasa-info-dark",
    },
  ];

  const filteredCategories = helpCategories.filter(
    (category) =>
      category.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      category.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      category.articles.some((article) =>
        article.toLowerCase().includes(searchQuery.toLowerCase())
      )
  );

  return (
    <div className="min-h-screen bg-locasa-background-light dark:bg-locasa-background-dark">
      {/* Hero Section */}
      <section className="py-20 lg:py-32 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-locasa-primary-light/5 to-locasa-success-light/5 dark:from-locasa-primary-dark/5 dark:to-locasa-success-dark/5"></div>

        <div className="relative max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-locasa-info-light/10 dark:bg-locasa-info-dark/10 text-locasa-info-light dark:text-locasa-info-dark text-sm font-medium mb-6">
            <HelpCircle className="h-4 w-4" />
            We're Here to Help
          </div>

          <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-locasa-text-light dark:text-locasa-text-dark mb-6 leading-tight">
            Help Center
          </h1>

          <p className="text-lg sm:text-xl text-locasa-secondary-light dark:text-locasa-secondary-dark mb-8 max-w-3xl mx-auto">
            Find answers to your questions, learn how to use Locasa, and get the
            support you need to make the most of our local marketplace.
          </p>

          {/* Search Bar */}
          <div className="relative max-w-2xl mx-auto">
            <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
              <Search className="h-5 w-5 text-locasa-secondary-light dark:text-locasa-secondary-dark" />
            </div>
            <input
              type="text"
              placeholder="Search for help articles..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-12 pr-4 py-4 rounded-xl bg-locasa-input-light dark:bg-locasa-input-dark border border-locasa-border-light/20 dark:border-locasa-border-dark/20 text-locasa-text-light dark:text-locasa-text-dark placeholder-locasa-secondary-light dark:placeholder-locasa-secondary-dark focus:outline-none focus:ring-2 focus:ring-locasa-primary-light dark:focus:ring-locasa-primary-dark text-lg"
            />
          </div>
        </div>
      </section>

      {/* Quick Actions */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-2xl font-bold text-locasa-text-light dark:text-locasa-text-dark text-center mb-12">
            Quick Actions
          </h2>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {quickActions.map((action, index) => {
              const Icon = action.icon;
              const isExternal =
                action.href.startsWith("mailto:") ||
                action.href.startsWith("tel:");

              const content = (
                <div
                  className={cn(
                    "p-6 rounded-2xl border border-locasa-border-light/20 dark:border-locasa-border-dark/20",
                    "bg-locasa-input-light/30 dark:bg-locasa-input-dark/30 backdrop-blur-sm",
                    "hover:border-locasa-primary-light/40 dark:hover:border-locasa-primary-dark/40",
                    "transition-all duration-300 hover:transform hover:scale-105",
                    "hover:shadow-lg hover:shadow-locasa-primary-light/10 dark:hover:shadow-locasa-primary-dark/10",
                    "text-center group cursor-pointer"
                  )}
                >
                  <div
                    className={cn(
                      "w-16 h-16 rounded-xl flex items-center justify-center mx-auto mb-4",
                      "group-hover:scale-110 transition-transform duration-300",
                      action.color
                    )}
                  >
                    <Icon className="h-8 w-8 text-locasa-background-light dark:text-white" />
                  </div>
                  <h3 className="text-lg font-bold text-locasa-text-light dark:text-locasa-text-dark mb-2">
                    {action.title}
                  </h3>
                  <p className="text-locasa-secondary-light dark:text-locasa-secondary-dark text-sm">
                    {action.description}
                  </p>
                </div>
              );

              return isExternal ? (
                <a key={index} href={action.href}>
                  {content}
                </a>
              ) : (
                <Link key={index} href={action.href}>
                  {content}
                </Link>
              );
            })}
          </div>
        </div>
      </section>

      {/* Help Categories */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-locasa-text-light dark:text-locasa-text-dark text-center mb-12">
            Browse Help Topics
          </h2>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredCategories.map((category, index) => {
              const Icon = category.icon;
              return (
                <div
                  key={category.id}
                  className={cn(
                    "p-8 rounded-2xl border border-locasa-border-light/20 dark:border-locasa-border-dark/20",
                    "bg-locasa-input-light/30 dark:bg-locasa-input-dark/30 backdrop-blur-sm",
                    "hover:border-locasa-primary-light/40 dark:hover:border-locasa-primary-dark/40",
                    "transition-all duration-300 hover:transform hover:scale-105",
                    "hover:shadow-lg hover:shadow-locasa-primary-light/10 dark:hover:shadow-locasa-primary-dark/10",
                    "group cursor-pointer"
                  )}
                >
                  <div className="flex items-center gap-4 mb-6">
                    <div
                      className={cn(
                        "w-12 h-12 rounded-xl bg-gradient-to-br flex items-center justify-center",
                        "group-hover:scale-110 transition-transform duration-300",
                        category.color
                      )}
                    >
                      <Icon className="h-6 w-6 text-locasa-background-light dark:text-white" />
                    </div>
                    <h3 className="text-xl font-bold text-locasa-text-light dark:text-locasa-text-dark">
                      {category.title}
                    </h3>
                  </div>

                  <p className="text-locasa-secondary-light dark:text-locasa-secondary-dark mb-6">
                    {category.description}
                  </p>

                  <ul className="space-y-2">
                    {category.articles.map((article, articleIndex) => (
                      <li key={articleIndex} className="flex items-start gap-2">
                        <div className="w-1.5 h-1.5 rounded-full bg-locasa-primary-light dark:bg-locasa-primary-dark mt-2 flex-shrink-0"></div>
                        <span className="text-locasa-secondary-light dark:text-locasa-secondary-dark text-sm hover:text-locasa-primary-light dark:hover:text-locasa-primary-dark transition-colors cursor-pointer">
                          {article}
                        </span>
                      </li>
                    ))}
                  </ul>
                </div>
              );
            })}
          </div>

          {filteredCategories.length === 0 && searchQuery && (
            <div className="text-center py-12">
              <HelpCircle className="h-16 w-16 text-locasa-secondary-light dark:text-locasa-secondary-dark mx-auto mb-4" />
              <h3 className="text-xl font-bold text-locasa-text-light dark:text-locasa-text-dark mb-2">
                No results found
              </h3>
              <p className="text-locasa-secondary-light dark:text-locasa-secondary-dark">
                Try searching with different keywords or browse our help
                categories above.
              </p>
            </div>
          )}
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-16">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="p-8 bg-gradient-to-br from-locasa-primary-light/10 to-locasa-success-light/10 dark:from-locasa-primary-dark/10 dark:to-locasa-success-dark/10 rounded-2xl border border-locasa-border-light/20 dark:border-locasa-border-dark/20">
            <MessageCircle className="h-12 w-12 text-locasa-primary-light dark:text-locasa-primary-dark mx-auto mb-4" />
            <h3 className="text-2xl font-bold text-locasa-text-light dark:text-locasa-text-dark mb-4">
              Still Need Help?
            </h3>
            <p className="text-locasa-secondary-light dark:text-locasa-secondary-dark mb-6">
              Can't find what you're looking for? Our support team is here to
              help you with any questions or issues.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/contact"
                className="inline-flex items-center gap-2 px-6 py-3 bg-locasa-primary-light dark:bg-locasa-primary-dark text-locasa-background-light dark:text-white rounded-lg hover:bg-locasa-primary-light/90 dark:hover:bg-locasa-primary-dark/90 transition-colors font-medium"
              >
                <MessageCircle className="h-4 w-4" />
                Contact Support
              </Link>
              <a
                href="mailto:<EMAIL>"
                className="inline-flex items-center gap-2 px-6 py-3 border border-locasa-primary-light dark:border-locasa-primary-dark text-locasa-primary-light dark:text-locasa-primary-dark rounded-lg hover:bg-locasa-primary-light/10 dark:hover:bg-locasa-primary-dark/10 transition-colors font-medium"
              >
                <Mail className="h-4 w-4" />
                Email Us
              </a>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
