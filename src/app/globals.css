@import "tailwindcss";

:root {
  /* Light theme colors */
  --locasa-primary-light: #2c3930;
  --locasa-primary-dark: #d0e7d2;
  --locasa-background-light: #f7f5fa;
  --locasa-background-dark: #0e0e0e;
  --locasa-text-light: #1c1c1c;
  --locasa-text-dark: #f1f1f1;
  --locasa-success-light: #2aa876;
  --locasa-success-dark: #81c995;
  --locasa-error-light: #e64848;
  --locasa-error-dark: #f28b82;
  --locasa-warning-light: #f4a100;
  --locasa-warning-dark: #ffc107;
  --locasa-info-light: #2680eb;
  --locasa-info-dark: #82b1ff;
  --locasa-input-light: #dcd7c9;
  --locasa-input-dark: #2e2e2e;
  --locasa-border-light: #555;
  --locasa-border-dark: #3a3a3a;
  --locasa-secondary-light: #555;
  --locasa-secondary-dark: #cccccc;
}

* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  margin: 0;
  padding: 0;
  min-height: 100vh;
  background-color: #f7f5fa;
  color: #1c1c1c;
  transition: background-color 0.3s ease, color 0.3s ease;
}

.dark body {
  background-color: #0e0e0e;
  color: #f1f1f1;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: #dcd7c9;
  border-radius: 4px;
}

.dark ::-webkit-scrollbar-thumb {
  background: #3a3a3a;
}

::-webkit-scrollbar-thumb:hover {
  background: #2c3930;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: #d0e7d2;
}

/* Custom animations for landing page */
@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fade-in-down {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-in-left {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slide-in-right {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.6s ease-out;
}

.animate-fade-in-up {
  animation: fade-in-up 0.6s ease-out;
}

.animate-fade-in-down {
  animation: fade-in-down 0.6s ease-out;
}

.animate-slide-in-left {
  animation: slide-in-left 0.6s ease-out;
}

.animate-slide-in-right {
  animation: slide-in-right 0.6s ease-out;
}

.delay-100 {
  animation-delay: 0.1s;
}

.delay-200 {
  animation-delay: 0.2s;
}

.delay-300 {
  animation-delay: 0.3s;
}

.delay-400 {
  animation-delay: 0.4s;
}

.delay-500 {
  animation-delay: 0.5s;
}

.delay-1000 {
  animation-delay: 1s;
}

/* Built-in animations for loading screen */
@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes bounce {
  0%,
  20%,
  53%,
  80%,
  100% {
    transform: translate3d(0, 0, 0);
  }
  40%,
  43% {
    transform: translate3d(0, -30px, 0);
  }
  70% {
    transform: translate3d(0, -15px, 0);
  }
  90% {
    transform: translate3d(0, -4px, 0);
  }
}
