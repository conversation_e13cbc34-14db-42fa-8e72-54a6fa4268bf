"use client";

import React, { useState } from "react";
import {
  Trash2,
  <PERSON><PERSON><PERSON><PERSON>gle,
  CheckCircle,
  Mail,
  Clock,
  Shield,
  FileText,
} from "lucide-react";
import { cn } from "@/lib/utils";

export default function DataDeletion() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Simulate form submission
    await new Promise((resolve) => setTimeout(resolve, 2000));

    setIsSubmitting(false);
    setIsSubmitted(true);
  };

  const deletionSteps = [
    {
      step: 1,
      title: "Submit Request",
      description: "Fill out the data deletion request form below",
      icon: FileText,
    },
    {
      step: 2,
      title: "Verification",
      description: "We'll verify your identity and account ownership",
      icon: Shield,
    },
    {
      step: 3,
      title: "Processing",
      description: "Your data will be permanently deleted within 30 days",
      icon: Clock,
    },
    {
      step: 4,
      title: "Confirmation",
      description: "You'll receive confirmation once deletion is complete",
      icon: CheckCircle,
    },
  ];

  const dataTypes = [
    "Personal account information (name, email, phone)",
    "Business profile and listing information",
    "Transaction history and payment data",
    "Messages and communication records",
    "Reviews and ratings you've given or received",
    "Usage analytics and behavioral data",
  ];

  if (isSubmitted) {
    return (
      <div className="min-h-screen bg-locasa-background-light dark:bg-locasa-background-dark flex items-center justify-center px-4">
        <div className="max-w-md w-full text-center">
          <div className="w-16 h-16 bg-locasa-success-light dark:bg-locasa-success-dark rounded-full flex items-center justify-center mx-auto mb-6">
            <CheckCircle className="h-8 w-8 text-locasa-background-light dark:text-white" />
          </div>
          <h1 className="text-2xl font-bold text-locasa-text-light dark:text-locasa-text-dark mb-4">
            Request Submitted Successfully
          </h1>
          <p className="text-locasa-secondary-light dark:text-locasa-secondary-dark mb-6">
            We've received your data deletion request. You'll receive a
            confirmation email shortly, and we'll process your request within 30
            days.
          </p>
          <button
            onClick={() => setIsSubmitted(false)}
            className="px-6 py-3 bg-locasa-primary-light dark:bg-locasa-primary-dark text-locasa-background-light dark:text-white rounded-lg hover:bg-locasa-primary-light/90 dark:hover:bg-locasa-primary-dark/90 transition-colors font-medium"
          >
            Submit Another Request
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-locasa-background-light dark:bg-locasa-background-dark">
      {/* Hero Section */}
      <section className="py-20 lg:py-32 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-locasa-error-light/5 to-locasa-warning-light/5 dark:from-locasa-error-dark/5 dark:to-locasa-warning-dark/5"></div>

        <div className="relative max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-locasa-error-light/10 dark:bg-locasa-error-dark/10 text-locasa-error-light dark:text-locasa-error-dark text-sm font-medium mb-6">
            <Trash2 className="h-4 w-4" />
            Data Deletion Request
          </div>

          <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-locasa-text-light dark:text-locasa-text-dark mb-6 leading-tight">
            Delete Your Data
          </h1>

          <p className="text-lg sm:text-xl text-locasa-secondary-light dark:text-locasa-secondary-dark mb-8 max-w-3xl mx-auto">
            You have the right to request deletion of your personal data. This
            process is permanent and cannot be undone. Please review the
            information below carefully.
          </p>
        </div>
      </section>

      {/* Warning Section */}
      <section className="py-8">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="p-6 bg-locasa-warning-light/10 dark:bg-locasa-warning-dark/10 border border-locasa-warning-light/20 dark:border-locasa-warning-dark/20 rounded-2xl">
            <div className="flex items-start gap-4">
              <AlertTriangle className="h-6 w-6 text-locasa-warning-light dark:text-locasa-warning-dark flex-shrink-0 mt-1" />
              <div>
                <h3 className="font-bold text-locasa-text-light dark:text-locasa-text-dark mb-2">
                  Important Notice
                </h3>
                <p className="text-locasa-secondary-light dark:text-locasa-secondary-dark text-sm leading-relaxed">
                  Data deletion is permanent and irreversible. Once processed,
                  you will lose access to your account, transaction history, and
                  all associated data. Some information may be retained for
                  legal compliance purposes.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Process Steps */}
      <section className="py-16">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-locasa-text-light dark:text-locasa-text-dark text-center mb-12">
            Deletion Process
          </h2>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {deletionSteps.map((step, index) => {
              const Icon = step.icon;
              return (
                <div key={step.step} className="text-center">
                  <div className="w-16 h-16 bg-gradient-to-br from-locasa-primary-light to-locasa-success-light dark:from-locasa-primary-dark dark:to-locasa-success-dark rounded-full flex items-center justify-center mx-auto mb-4">
                    <Icon className="h-8 w-8 text-locasa-background-light dark:text-white" />
                  </div>
                  <div className="text-sm font-medium text-locasa-primary-light dark:text-locasa-primary-dark mb-2">
                    Step {step.step}
                  </div>
                  <h3 className="font-bold text-locasa-text-light dark:text-locasa-text-dark mb-2">
                    {step.title}
                  </h3>
                  <p className="text-sm text-locasa-secondary-light dark:text-locasa-secondary-dark">
                    {step.description}
                  </p>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Data Types Section */}
      <section className="py-16">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="p-8 bg-locasa-input-light/30 dark:bg-locasa-input-dark/30 rounded-2xl border border-locasa-border-light/20 dark:border-locasa-border-dark/20">
            <h3 className="text-2xl font-bold text-locasa-text-light dark:text-locasa-text-dark mb-6">
              Data That Will Be Deleted
            </h3>
            <div className="grid md:grid-cols-2 gap-4">
              {dataTypes.map((dataType, index) => (
                <div key={index} className="flex items-start gap-3">
                  <div className="w-2 h-2 rounded-full bg-locasa-error-light dark:bg-locasa-error-dark mt-2 flex-shrink-0"></div>
                  <span className="text-locasa-secondary-light dark:text-locasa-secondary-dark text-sm">
                    {dataType}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Request Form */}
      <section className="py-16">
        <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="p-8 bg-locasa-input-light/30 dark:bg-locasa-input-dark/30 rounded-2xl border border-locasa-border-light/20 dark:border-locasa-border-dark/20">
            <h3 className="text-2xl font-bold text-locasa-text-light dark:text-locasa-text-dark mb-6 text-center">
              Submit Deletion Request
            </h3>

            <form onSubmit={handleSubmit} className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-locasa-text-light dark:text-locasa-text-dark mb-2">
                  Email Address
                </label>
                <input
                  type="email"
                  required
                  className="w-full px-4 py-3 rounded-lg bg-locasa-background-light dark:bg-locasa-background-dark border border-locasa-border-light dark:border-locasa-border-dark text-locasa-text-light dark:text-locasa-text-dark focus:outline-none focus:ring-2 focus:ring-locasa-primary-light dark:focus:ring-locasa-primary-dark"
                  placeholder="Enter your account email"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-locasa-text-light dark:text-locasa-text-dark mb-2">
                  Account Type
                </label>
                <select
                  required
                  className="w-full px-4 py-3 rounded-lg bg-locasa-background-light dark:bg-locasa-background-dark border border-locasa-border-light dark:border-locasa-border-dark text-locasa-text-light dark:text-locasa-text-dark focus:outline-none focus:ring-2 focus:ring-locasa-primary-light dark:focus:ring-locasa-primary-dark"
                >
                  <option value="">Select account type</option>
                  <option value="customer">Customer Account</option>
                  <option value="vendor">Vendor Account</option>
                  <option value="both">Both Customer & Vendor</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-locasa-text-light dark:text-locasa-text-dark mb-2">
                  Reason for Deletion (Optional)
                </label>
                <textarea
                  rows={4}
                  className="w-full px-4 py-3 rounded-lg bg-locasa-background-light dark:bg-locasa-background-dark border border-locasa-border-light dark:border-locasa-border-dark text-locasa-text-light dark:text-locasa-text-dark focus:outline-none focus:ring-2 focus:ring-locasa-primary-light dark:focus:ring-locasa-primary-dark"
                  placeholder="Please let us know why you're deleting your account (optional)"
                />
              </div>

              <div className="flex items-start gap-3">
                <input
                  type="checkbox"
                  id="confirm"
                  required
                  className="mt-1 w-4 h-4 text-locasa-primary-light dark:text-locasa-primary-dark bg-locasa-background-light dark:bg-locasa-background-dark border border-locasa-border-light dark:border-locasa-border-dark rounded focus:ring-locasa-primary-light dark:focus:ring-locasa-primary-dark"
                />
                <label
                  htmlFor="confirm"
                  className="text-sm text-locasa-secondary-light dark:text-locasa-secondary-dark"
                >
                  I understand that this action is permanent and irreversible.
                  All my data will be permanently deleted and cannot be
                  recovered.
                </label>
              </div>

              <button
                type="submit"
                disabled={isSubmitting}
                className={cn(
                  "w-full flex items-center justify-center gap-2 px-6 py-3 rounded-lg font-medium",
                  "bg-locasa-error-light dark:bg-locasa-error-dark text-locasa-background-light dark:text-white",
                  "hover:bg-locasa-error-light/90 dark:hover:bg-locasa-error-dark/90",
                  "focus:outline-none focus:ring-2 focus:ring-locasa-error-light dark:focus:ring-locasa-error-dark",
                  "transition-colors duration-200",
                  "disabled:opacity-50 disabled:cursor-not-allowed"
                )}
              >
                {isSubmitting ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    Processing...
                  </>
                ) : (
                  <>
                    <Trash2 className="h-4 w-4" />
                    Submit Deletion Request
                  </>
                )}
              </button>
            </form>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-16">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="p-8 bg-locasa-input-light/30 dark:bg-locasa-input-dark/30 rounded-2xl border border-locasa-border-light/20 dark:border-locasa-border-dark/20">
            <Mail className="h-12 w-12 text-locasa-primary-light dark:text-locasa-primary-dark mx-auto mb-4" />
            <h3 className="text-2xl font-bold text-locasa-text-light dark:text-locasa-text-dark mb-4">
              Need Help?
            </h3>
            <p className="text-locasa-secondary-light dark:text-locasa-secondary-dark mb-6">
              If you have questions about data deletion or need assistance with
              your request, please contact our support team.
            </p>
            <a
              href="mailto:<EMAIL>"
              className="inline-flex items-center gap-2 px-6 py-3 bg-locasa-primary-light dark:bg-locasa-primary-dark text-locasa-background-light dark:text-white rounded-lg hover:bg-locasa-primary-light/90 dark:hover:bg-locasa-primary-dark/90 transition-colors font-medium"
            >
              <Mail className="h-4 w-4" />
              <EMAIL>
            </a>
          </div>
        </div>
      </section>
    </div>
  );
}
