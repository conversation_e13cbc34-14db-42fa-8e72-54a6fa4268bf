"use client";

import Link from "next/link";
import { Home, ArrowLeft } from "lucide-react";

export default function NotFound() {
  return (
    <div className="min-h-screen bg-locasa-background-light dark:bg-locasa-background-dark flex items-center justify-center px-4">
      <div className="text-center">
        <div className="mb-8">
          <h1 className="text-9xl font-bold text-locasa-primary-light dark:text-locasa-primary-dark mb-4">
            404
          </h1>
          <h2 className="text-2xl font-semibold text-locasa-text-light dark:text-locasa-text-dark mb-2">
            Page Not Found
          </h2>
          <p className="text-locasa-secondary-light dark:text-locasa-secondary-dark max-w-md mx-auto">
            Sorry, we couldn't find the page you're looking for. It might have
            been moved, deleted, or you entered the wrong URL.
          </p>
        </div>

        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link
            href="/"
            className="inline-flex items-center gap-2 px-6 py-3 bg-locasa-primary-light dark:bg-locasa-primary-dark text-locasa-background-light dark:text-white rounded-lg hover:bg-locasa-primary-light/90 dark:hover:bg-locasa-primary-dark/90 transition-colors font-medium"
          >
            <Home className="h-4 w-4" />
            Go Home
          </Link>

          <button
            onClick={() => window.history.back()}
            className="inline-flex items-center gap-2 px-6 py-3 border border-locasa-border-light dark:border-locasa-border-dark text-locasa-text-light dark:text-locasa-text-dark rounded-lg hover:bg-locasa-input-light dark:hover:bg-locasa-input-dark transition-colors font-medium"
          >
            <ArrowLeft className="h-4 w-4" />
            Go Back
          </button>
        </div>
      </div>
    </div>
  );
}
