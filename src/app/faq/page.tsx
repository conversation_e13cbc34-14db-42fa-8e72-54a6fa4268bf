"use client";

import React, { useState } from "react";
import {
  ChevronDown,
  ChevronUp,
  HelpCircle,
  Search,
  MessageCircle,
  Users,
  ShoppingBag,
  CreditCard,
  Shield,
  Settings,
} from "lucide-react";
import { cn } from "@/lib/utils";
import Link from "next/link";

export default function FAQ() {
  const [searchQuery, setSearchQuery] = useState("");
  const [openItems, setOpenItems] = useState<string[]>([]);

  const toggleItem = (id: string) => {
    setOpenItems((prev) =>
      prev.includes(id) ? prev.filter((item) => item !== id) : [...prev, id]
    );
  };

  const faqCategories = [
    {
      id: "general",
      title: "General Questions",
      icon: HelpCircle,
      color:
        "from-locasa-primary-light to-locasa-success-light dark:from-locasa-primary-dark dark:to-locasa-success-dark",
      questions: [
        {
          id: "what-is-locasa",
          question: "What is Locasa?",
          answer:
            "Locasa is a local marketplace platform that connects customers with authentic local businesses and vendors in their community. We help you discover unique products and services while supporting your local economy.",
        },
        {
          id: "how-does-it-work",
          question: "How does Locasa work?",
          answer:
            "Customers can browse local businesses, view products and services, and make purchases directly through our platform. Vendors can create profiles, list their offerings, and manage their business operations through our vendor dashboard.",
        },
        {
          id: "is-it-free",
          question: "Is Locasa free to use?",
          answer:
            "Yes, creating an account and browsing the marketplace is completely free for customers. Vendors may have fees associated with certain premium features or transaction processing.",
        },
      ],
    },
    {
      id: "account",
      title: "Account & Profile",
      icon: Users,
      color:
        "from-locasa-success-light to-locasa-warning-light dark:from-locasa-success-dark dark:to-locasa-warning-dark",
      questions: [
        {
          id: "create-account",
          question: "How do I create an account?",
          answer:
            "Click the 'Sign Up' button on our homepage, choose whether you're a customer or vendor, and fill out the registration form with your basic information. You'll receive a confirmation email to verify your account.",
        },
        {
          id: "forgot-password",
          question: "I forgot my password. How can I reset it?",
          answer:
            "Click 'Forgot Password' on the login page, enter your email address, and we'll send you a secure link to reset your password. The link expires after 24 hours for security.",
        },
        {
          id: "update-profile",
          question: "How do I update my profile information?",
          answer:
            "Log into your account and go to Settings > Profile. You can update your personal information, contact details, and preferences. Changes are saved automatically.",
        },
      ],
    },
    {
      id: "buying",
      title: "Buying & Orders",
      icon: ShoppingBag,
      color:
        "from-locasa-warning-light to-locasa-error-light dark:from-locasa-warning-dark dark:to-locasa-error-dark",
      questions: [
        {
          id: "place-order",
          question: "How do I place an order?",
          answer:
            "Browse products, add items to your cart, and proceed to checkout. Enter your delivery information and payment details to complete your order. You'll receive a confirmation email with order details.",
        },
        {
          id: "track-order",
          question: "How can I track my order?",
          answer:
            "You can track your order status in your account dashboard under 'My Orders'. You'll also receive email updates when your order status changes, including shipping and delivery notifications.",
        },
        {
          id: "cancel-order",
          question: "Can I cancel my order?",
          answer:
            "Orders can typically be cancelled within 1 hour of placement, depending on the vendor's processing time. Contact the vendor directly or use the 'Cancel Order' option in your account if available.",
        },
      ],
    },
    {
      id: "payments",
      title: "Payments & Refunds",
      icon: CreditCard,
      color:
        "from-locasa-error-light to-locasa-info-light dark:from-locasa-error-dark dark:to-locasa-info-dark",
      questions: [
        {
          id: "payment-methods",
          question: "What payment methods do you accept?",
          answer:
            "We accept major credit cards (Visa, MasterCard, American Express), debit cards, PayPal, and digital wallets like Apple Pay and Google Pay. All payments are processed securely through encrypted channels.",
        },
        {
          id: "refund-policy",
          question: "What is your refund policy?",
          answer:
            "Refund policies vary by vendor. Generally, you can request a refund within 7-14 days of purchase for unused items. Digital products and custom orders may have different policies. Check the vendor's specific policy before purchasing.",
        },
        {
          id: "payment-security",
          question: "Is my payment information secure?",
          answer:
            "Yes, we use industry-standard SSL encryption and PCI-compliant payment processors. We never store your complete payment information on our servers. All transactions are monitored for fraud protection.",
        },
      ],
    },
    {
      id: "vendors",
      title: "For Vendors",
      icon: Settings,
      color:
        "from-locasa-info-light to-locasa-primary-light dark:from-locasa-info-dark dark:to-locasa-primary-dark",
      questions: [
        {
          id: "become-vendor",
          question: "How do I become a vendor on Locasa?",
          answer:
            "Click 'Become a Vendor' and complete the vendor application form. You'll need to provide business information, verify your identity, and agree to our vendor terms. Approval typically takes 2-3 business days.",
        },
        {
          id: "vendor-fees",
          question: "What fees do vendors pay?",
          answer:
            "We charge a small transaction fee on completed sales, typically 3-5% depending on your plan. There are no monthly fees for basic accounts. Premium features and marketing tools may have additional costs.",
        },
        {
          id: "manage-inventory",
          question: "How do I manage my inventory?",
          answer:
            "Use the vendor dashboard to add products, update quantities, set prices, and manage your inventory. You can bulk upload products via CSV and set up automatic low-stock alerts.",
        },
      ],
    },
    {
      id: "security",
      title: "Security & Privacy",
      icon: Shield,
      color:
        "from-locasa-primary-light to-locasa-success-light dark:from-locasa-primary-dark dark:to-locasa-success-dark",
      questions: [
        {
          id: "data-protection",
          question: "How do you protect my personal data?",
          answer:
            "We follow strict data protection protocols and comply with privacy regulations. Your data is encrypted, stored securely, and never sold to third parties. See our Privacy Policy for complete details.",
        },
        {
          id: "account-security",
          question: "How can I keep my account secure?",
          answer:
            "Use a strong, unique password and enable two-factor authentication. Never share your login credentials and log out from shared devices. Report any suspicious activity immediately.",
        },
        {
          id: "delete-account",
          question: "How do I delete my account?",
          answer:
            "You can request account deletion through our Data Deletion page or by contacting support. This action is permanent and will remove all your data from our systems within 30 days.",
        },
      ],
    },
  ];

  const allQuestions = faqCategories.flatMap((category) =>
    category.questions.map((q) => ({
      ...q,
      categoryId: category.id,
      categoryTitle: category.title,
    }))
  );

  const filteredQuestions = searchQuery
    ? allQuestions.filter(
        (q) =>
          q.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
          q.answer.toLowerCase().includes(searchQuery.toLowerCase())
      )
    : [];

  const displayCategories = searchQuery ? [] : faqCategories;

  return (
    <div className="min-h-screen bg-locasa-background-light dark:bg-locasa-background-dark">
      {/* Hero Section */}
      <section className="py-20 lg:py-32 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-locasa-primary-light/5 to-locasa-success-light/5 dark:from-locasa-primary-dark/5 dark:to-locasa-success-dark/5"></div>

        <div className="relative max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-locasa-success-light/10 dark:bg-locasa-success-dark/10 text-locasa-success-light dark:text-locasa-success-dark text-sm font-medium mb-6">
            <HelpCircle className="h-4 w-4" />
            Frequently Asked Questions
          </div>

          <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-locasa-text-light dark:text-locasa-text-dark mb-6 leading-tight">
            FAQ
          </h1>

          <p className="text-lg sm:text-xl text-locasa-secondary-light dark:text-locasa-secondary-dark mb-8 max-w-3xl mx-auto">
            Find quick answers to the most common questions about using Locasa.
            Can't find what you're looking for? Contact our support team.
          </p>

          {/* Search Bar */}
          <div className="relative max-w-2xl mx-auto">
            <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
              <Search className="h-5 w-5 text-locasa-secondary-light dark:text-locasa-secondary-dark" />
            </div>
            <input
              type="text"
              placeholder="Search FAQ..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-12 pr-4 py-4 rounded-xl bg-locasa-input-light dark:bg-locasa-input-dark border border-locasa-border-light/20 dark:border-locasa-border-dark/20 text-locasa-text-light dark:text-locasa-text-dark placeholder-locasa-secondary-light dark:placeholder-locasa-secondary-dark focus:outline-none focus:ring-2 focus:ring-locasa-primary-light dark:focus:ring-locasa-primary-dark text-lg"
            />
          </div>
        </div>
      </section>

      {/* Search Results */}
      {searchQuery && (
        <section className="py-8">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 className="text-2xl font-bold text-locasa-text-light dark:text-locasa-text-dark mb-6">
              Search Results ({filteredQuestions.length})
            </h2>

            {filteredQuestions.length > 0 ? (
              <div className="space-y-4">
                {filteredQuestions.map((item) => (
                  <div
                    key={item.id}
                    className="p-6 bg-locasa-input-light/30 dark:bg-locasa-input-dark/30 rounded-2xl border border-locasa-border-light/20 dark:border-locasa-border-dark/20"
                  >
                    <div className="flex items-start justify-between gap-4 mb-3">
                      <h3 className="font-bold text-locasa-text-light dark:text-locasa-text-dark">
                        {item.question}
                      </h3>
                      <span className="text-xs px-2 py-1 bg-locasa-primary-light/10 dark:bg-locasa-primary-dark/10 text-locasa-primary-light dark:text-locasa-primary-dark rounded-full whitespace-nowrap">
                        {item.categoryTitle}
                      </span>
                    </div>
                    <p className="text-locasa-secondary-light dark:text-locasa-secondary-dark leading-relaxed">
                      {item.answer}
                    </p>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <HelpCircle className="h-16 w-16 text-locasa-secondary-light dark:text-locasa-secondary-dark mx-auto mb-4" />
                <h3 className="text-xl font-bold text-locasa-text-light dark:text-locasa-text-dark mb-2">
                  No results found
                </h3>
                <p className="text-locasa-secondary-light dark:text-locasa-secondary-dark">
                  Try different keywords or browse the categories below.
                </p>
              </div>
            )}
          </div>
        </section>
      )}

      {/* FAQ Categories */}
      {displayCategories.length > 0 && (
        <section className="py-16">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="space-y-8">
              {displayCategories.map((category) => {
                const Icon = category.icon;
                return (
                  <div key={category.id} className="space-y-4">
                    <div className="flex items-center gap-4 mb-6">
                      <div
                        className={cn(
                          "w-12 h-12 rounded-xl bg-gradient-to-br flex items-center justify-center",
                          category.color
                        )}
                      >
                        <Icon className="h-6 w-6 text-locasa-background-light dark:text-white" />
                      </div>
                      <h2 className="text-2xl font-bold text-locasa-text-light dark:text-locasa-text-dark">
                        {category.title}
                      </h2>
                    </div>

                    <div className="space-y-3">
                      {category.questions.map((item) => {
                        const isOpen = openItems.includes(item.id);
                        return (
                          <div
                            key={item.id}
                            className="border border-locasa-border-light/20 dark:border-locasa-border-dark/20 rounded-xl bg-locasa-input-light/30 dark:bg-locasa-input-dark/30 overflow-hidden"
                          >
                            <button
                              onClick={() => toggleItem(item.id)}
                              className="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-locasa-primary-light/5 dark:hover:bg-locasa-primary-dark/5 transition-colors"
                            >
                              <span className="font-semibold text-locasa-text-light dark:text-locasa-text-dark">
                                {item.question}
                              </span>
                              {isOpen ? (
                                <ChevronUp className="h-5 w-5 text-locasa-primary-light dark:text-locasa-primary-dark flex-shrink-0" />
                              ) : (
                                <ChevronDown className="h-5 w-5 text-locasa-primary-light dark:text-locasa-primary-dark flex-shrink-0" />
                              )}
                            </button>

                            {isOpen && (
                              <div className="px-6 pb-4 border-t border-locasa-border-light/10 dark:border-locasa-border-dark/10">
                                <p className="text-locasa-secondary-light dark:text-locasa-secondary-dark leading-relaxed pt-4">
                                  {item.answer}
                                </p>
                              </div>
                            )}
                          </div>
                        );
                      })}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </section>
      )}

      {/* Contact Section */}
      <section className="py-16">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="p-8 bg-gradient-to-br from-locasa-primary-light/10 to-locasa-success-light/10 dark:from-locasa-primary-dark/10 dark:to-locasa-success-dark/10 rounded-2xl border border-locasa-border-light/20 dark:border-locasa-border-dark/20">
            <MessageCircle className="h-12 w-12 text-locasa-primary-light dark:text-locasa-primary-dark mx-auto mb-4" />
            <h3 className="text-2xl font-bold text-locasa-text-light dark:text-locasa-text-dark mb-4">
              Still Have Questions?
            </h3>
            <p className="text-locasa-secondary-light dark:text-locasa-secondary-dark mb-6">
              Can't find the answer you're looking for? Our support team is
              ready to help you.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/contact"
                className="inline-flex items-center gap-2 px-6 py-3 bg-locasa-primary-light dark:bg-locasa-primary-dark text-locasa-background-light dark:text-white rounded-lg hover:bg-locasa-primary-light/90 dark:hover:bg-locasa-primary-dark/90 transition-colors font-medium"
              >
                <MessageCircle className="h-4 w-4" />
                Contact Support
              </Link>
              <Link
                href="/help"
                className="inline-flex items-center gap-2 px-6 py-3 border border-locasa-primary-light dark:border-locasa-primary-dark text-locasa-primary-light dark:text-locasa-primary-dark rounded-lg hover:bg-locasa-primary-light/10 dark:hover:bg-locasa-primary-dark/10 transition-colors font-medium"
              >
                <HelpCircle className="h-4 w-4" />
                Help Center
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
