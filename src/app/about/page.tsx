"use client";

import React from "react";
import { useTranslations } from "next-intl";
import { Users, Target, Heart, Award, Globe, Handshake } from "lucide-react";
import { cn } from "@/lib/utils";

export default function AboutPage() {
  const values = [
    {
      icon: Heart,
      title: "Community First",
      description:
        "We believe in the power of local communities and supporting neighborhood businesses.",
      color: "text-locasa-error-light dark:text-locasa-error-dark",
      bgColor: "bg-locasa-error-light/10 dark:bg-locasa-error-dark/10",
    },
    {
      icon: Handshake,
      title: "Trust & Authenticity",
      description:
        "Every vendor is verified to ensure authentic local products and services.",
      color: "text-locasa-info-light dark:text-locasa-info-dark",
      bgColor: "bg-locasa-info-light/10 dark:bg-locasa-info-dark/10",
    },
    {
      icon: Globe,
      title: "Sustainable Growth",
      description:
        "Promoting sustainable business practices and environmental responsibility.",
      color: "text-locasa-success-light dark:text-locasa-success-dark",
      bgColor: "bg-locasa-success-light/10 dark:bg-locasa-success-dark/10",
    },
    {
      icon: Award,
      title: "Quality Excellence",
      description:
        "Committed to maintaining the highest standards of product and service quality.",
      color: "text-locasa-warning-light dark:text-locasa-warning-dark",
      bgColor: "bg-locasa-warning-light/10 dark:bg-locasa-warning-dark/10",
    },
  ];

  const stats = [
    { number: "1000+", label: "Local Vendors" },
    { number: "5000+", label: "Happy Customers" },
    { number: "50+", label: "Cities" },
    { number: "10000+", label: "Products Sold" },
  ];

  const team = [
    {
      name: "Sarah Johnson",
      role: "CEO & Founder",
      description:
        "Passionate about supporting local businesses and building stronger communities.",
    },
    {
      name: "Michael Chen",
      role: "CTO",
      description:
        "Technology enthusiast focused on creating seamless user experiences.",
    },
    {
      name: "Emily Rodriguez",
      role: "Head of Community",
      description:
        "Dedicated to fostering relationships between vendors and customers.",
    },
  ];

  return (
    <div className="min-h-screen bg-locasa-background-light dark:bg-locasa-background-dark">
      {/* Hero Section */}
      <section className="relative py-20 lg:py-32">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-locasa-text-light dark:text-locasa-text-dark mb-6">
              About Locasa
            </h1>
            <p className="text-xl text-locasa-secondary-light dark:text-locasa-secondary-dark max-w-3xl mx-auto mb-8">
              We're on a mission to strengthen local communities by connecting
              people with authentic local businesses and artisans.
            </p>
            <div className="inline-flex items-center gap-2 px-6 py-3 rounded-full bg-locasa-success-light/10 dark:bg-locasa-success-dark/10 text-locasa-success-light dark:text-locasa-success-dark">
              <Users className="h-5 w-5" />
              Building Communities Since 2024
            </div>
          </div>
        </div>
      </section>

      {/* Mission Section */}
      <section className="py-16 bg-locasa-input-light dark:bg-locasa-input-dark">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-locasa-primary-light/10 dark:bg-locasa-primary-dark/10 text-locasa-primary-light dark:text-locasa-primary-dark text-sm font-medium mb-6">
                <Target className="h-4 w-4" />
                Our Mission
              </div>
              <h2 className="text-3xl font-bold text-locasa-text-light dark:text-locasa-text-dark mb-6">
                Empowering Local Businesses to Thrive
              </h2>
              <p className="text-lg text-locasa-secondary-light dark:text-locasa-secondary-dark mb-6">
                Locasa was born from the belief that local businesses are the
                heart of thriving communities. We provide a platform where
                authentic local vendors can showcase their products and services
                to customers who value quality, authenticity, and community
                connection.
              </p>
              <p className="text-lg text-locasa-secondary-light dark:text-locasa-secondary-dark">
                By choosing local, we're not just making purchases – we're
                investing in our neighborhoods, supporting our neighbors, and
                building a more sustainable future together.
              </p>
            </div>
            <div className="relative">
              <div className="aspect-square bg-gradient-to-br from-locasa-primary-light/20 to-locasa-success-light/20 dark:from-locasa-primary-dark/20 dark:to-locasa-success-dark/20 rounded-2xl flex items-center justify-center">
                <div className="text-center">
                  <Users className="h-24 w-24 text-locasa-primary-light dark:text-locasa-primary-dark mx-auto mb-4" />
                  <p className="text-lg font-semibold text-locasa-text-light dark:text-locasa-text-dark">
                    Connecting Communities
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-locasa-text-light dark:text-locasa-text-dark mb-4">
              Our Values
            </h2>
            <p className="text-lg text-locasa-secondary-light dark:text-locasa-secondary-dark max-w-2xl mx-auto">
              These core principles guide everything we do at Locasa
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <div key={index} className="text-center">
                <div
                  className={cn(
                    "inline-flex p-4 rounded-2xl mb-4",
                    value.bgColor
                  )}
                >
                  <value.icon className={cn("h-8 w-8", value.color)} />
                </div>
                <h3 className="text-xl font-semibold text-locasa-text-light dark:text-locasa-text-dark mb-3">
                  {value.title}
                </h3>
                <p className="text-locasa-secondary-light dark:text-locasa-secondary-dark">
                  {value.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-locasa-primary-light dark:bg-locasa-primary-dark">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-locasa-background-light dark:text-white mb-4">
              Our Impact
            </h2>
            <p className="text-lg text-locasa-background-light/80 dark:text-white/80 max-w-2xl mx-auto">
              Together, we're making a difference in communities across the
              region
            </p>
          </div>

          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="text-4xl lg:text-5xl font-bold text-locasa-background-light dark:text-white mb-2">
                  {stat.number}
                </div>
                <div className="text-locasa-background-light/80 dark:text-white/80 font-medium">
                  {stat.label}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-locasa-text-light dark:text-locasa-text-dark mb-4">
              Meet Our Team
            </h2>
            <p className="text-lg text-locasa-secondary-light dark:text-locasa-secondary-dark max-w-2xl mx-auto">
              The passionate people behind Locasa who are dedicated to
              supporting local communities
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {team.map((member, index) => (
              <div key={index} className="text-center">
                <div className="w-32 h-32 bg-gradient-to-br from-locasa-primary-light/20 to-locasa-success-light/20 dark:from-locasa-primary-dark/20 dark:to-locasa-success-dark/20 rounded-full mx-auto mb-6 flex items-center justify-center">
                  <Users className="h-16 w-16 text-locasa-primary-light dark:text-locasa-primary-dark" />
                </div>
                <h3 className="text-xl font-semibold text-locasa-text-light dark:text-locasa-text-dark mb-2">
                  {member.name}
                </h3>
                <p className="text-locasa-primary-light dark:text-locasa-primary-dark font-medium mb-3">
                  {member.role}
                </p>
                <p className="text-locasa-secondary-light dark:text-locasa-secondary-dark">
                  {member.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-locasa-input-light dark:bg-locasa-input-dark">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-locasa-text-light dark:text-locasa-text-dark mb-4">
            Ready to Join Our Community?
          </h2>
          <p className="text-lg text-locasa-secondary-light dark:text-locasa-secondary-dark mb-8">
            Whether you're a local business owner or someone who loves
            supporting local, we'd love to have you join us.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button className="px-8 py-3 bg-locasa-primary-light dark:bg-locasa-primary-dark text-locasa-background-light dark:text-white rounded-lg hover:bg-locasa-primary-light/90 dark:hover:bg-locasa-primary-dark/90 transition-colors font-medium">
              Become a Vendor
            </button>
            <button className="px-8 py-3 border border-locasa-primary-light dark:border-locasa-primary-dark text-locasa-primary-light dark:text-locasa-primary-dark rounded-lg hover:bg-locasa-primary-light/10 dark:hover:bg-locasa-primary-dark/10 transition-colors font-medium">
              Explore Marketplace
            </button>
          </div>
        </div>
      </section>
    </div>
  );
}
