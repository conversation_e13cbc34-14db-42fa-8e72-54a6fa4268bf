"use client";

import React, { useState } from "react";
import { useTranslations } from "next-intl";
import { Mail, Phone, MapPin, Send, MessageCircle, Clock } from "lucide-react";
import { cn } from "@/lib/utils";

export default function ContactPage() {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    subject: "",
    message: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Simulate form submission
    await new Promise((resolve) => setTimeout(resolve, 2000));

    // Reset form
    setFormData({ name: "", email: "", subject: "", message: "" });
    setIsSubmitting(false);

    // Show success message (you can implement a toast notification here)
    alert("Thank you for your message! We'll get back to you soon.");
  };

  const contactInfo = [
    {
      icon: Mail,
      title: "Email Us",
      details: "<EMAIL>",
      description: "Send us an email anytime",
      color: "text-locasa-info-light dark:text-locasa-info-dark",
      bgColor: "bg-locasa-info-light/10 dark:bg-locasa-info-dark/10",
    },
    {
      icon: Phone,
      title: "Call Us",
      details: "+****************",
      description: "Mon-Fri from 8am to 6pm",
      color: "text-locasa-success-light dark:text-locasa-success-dark",
      bgColor: "bg-locasa-success-light/10 dark:bg-locasa-success-dark/10",
    },
    {
      icon: MapPin,
      title: "Visit Us",
      details: "123 Local Street, Community City",
      description: "Our headquarters",
      color: "text-locasa-error-light dark:text-locasa-error-dark",
      bgColor: "bg-locasa-error-light/10 dark:bg-locasa-error-dark/10",
    },
  ];

  const faqs = [
    {
      question: "How do I become a vendor on Locasa?",
      answer:
        "Simply sign up as a vendor, complete your profile, and submit your application. Our team will review and approve qualified local businesses.",
    },
    {
      question: "Is there a fee to use Locasa?",
      answer:
        "Browsing and purchasing is free for customers. Vendors pay a small commission on successful sales to help maintain the platform.",
    },
    {
      question: "How do you verify local businesses?",
      answer:
        "We verify each vendor's business license, location, and authenticity to ensure they are genuine local businesses.",
    },
    {
      question: "What payment methods do you accept?",
      answer:
        "We accept all major credit cards, debit cards, and digital payment methods for secure transactions.",
    },
  ];

  return (
    <div className="min-h-screen bg-locasa-background-light dark:bg-locasa-background-dark">
      {/* Hero Section */}
      <section className="py-20 lg:py-32">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-locasa-text-light dark:text-locasa-text-dark mb-6">
              Get in Touch
            </h1>
            <p className="text-xl text-locasa-secondary-light dark:text-locasa-secondary-dark max-w-3xl mx-auto mb-8">
              Have questions about Locasa? We'd love to hear from you. Send us a
              message and we'll respond as soon as possible.
            </p>
          </div>
        </div>
      </section>

      {/* Contact Info Cards */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-3 gap-8 mb-16">
            {contactInfo.map((info, index) => (
              <div
                key={index}
                className="bg-locasa-background-light dark:bg-locasa-input-dark rounded-xl p-8 shadow-sm hover:shadow-md transition-shadow text-center"
              >
                <div
                  className={cn(
                    "inline-flex p-4 rounded-2xl mb-4",
                    info.bgColor
                  )}
                >
                  <info.icon className={cn("h-8 w-8", info.color)} />
                </div>
                <h3 className="text-xl font-semibold text-locasa-text-light dark:text-locasa-text-dark mb-2">
                  {info.title}
                </h3>
                <p className="text-lg font-medium text-locasa-primary-light dark:text-locasa-primary-dark mb-2">
                  {info.details}
                </p>
                <p className="text-locasa-secondary-light dark:text-locasa-secondary-dark">
                  {info.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Form & FAQ */}
      <section className="py-16 bg-locasa-input-light dark:bg-locasa-input-dark">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12">
            {/* Contact Form */}
            <div>
              <div className="bg-locasa-background-light dark:bg-locasa-background-dark rounded-xl p-8 shadow-sm">
                <div className="flex items-center gap-3 mb-6">
                  <MessageCircle className="h-6 w-6 text-locasa-primary-light dark:text-locasa-primary-dark" />
                  <h2 className="text-2xl font-bold text-locasa-text-light dark:text-locasa-text-dark">
                    Send us a Message
                  </h2>
                </div>

                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <label
                        htmlFor="name"
                        className="block text-sm font-medium text-locasa-text-light dark:text-locasa-text-dark mb-2"
                      >
                        Name
                      </label>
                      <input
                        type="text"
                        id="name"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        required
                        className="w-full px-4 py-3 rounded-lg border border-locasa-border-light/30 dark:border-locasa-border-dark/30 bg-locasa-input-light dark:bg-locasa-input-dark text-locasa-text-light dark:text-locasa-text-dark placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-locasa-primary-light dark:focus:ring-locasa-primary-dark"
                        placeholder="Your name"
                      />
                    </div>
                    <div>
                      <label
                        htmlFor="email"
                        className="block text-sm font-medium text-locasa-text-light dark:text-locasa-text-dark mb-2"
                      >
                        Email
                      </label>
                      <input
                        type="email"
                        id="email"
                        name="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        required
                        className="w-full px-4 py-3 rounded-lg border border-locasa-border-light/30 dark:border-locasa-border-dark/30 bg-locasa-input-light dark:bg-locasa-input-dark text-locasa-text-light dark:text-locasa-text-dark placeholder-locasa-secondary-light dark:placeholder-locasa-secondary-dark focus:outline-none focus:ring-2 focus:ring-locasa-primary-light dark:focus:ring-locasa-primary-dark"
                        placeholder="<EMAIL>"
                      />
                    </div>
                  </div>

                  <div>
                    <label
                      htmlFor="subject"
                      className="block text-sm font-medium text-locasa-text-light dark:text-locasa-text-dark mb-2"
                    >
                      Subject
                    </label>
                    <input
                      type="text"
                      id="subject"
                      name="subject"
                      value={formData.subject}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 rounded-lg border border-locasa-border-light/30 dark:border-locasa-border-dark/30 bg-locasa-input-light dark:bg-locasa-input-dark text-locasa-text-light dark:text-locasa-text-dark placeholder-locasa-secondary-light dark:placeholder-locasa-secondary-dark focus:outline-none focus:ring-2 focus:ring-locasa-primary-light dark:focus:ring-locasa-primary-dark"
                      placeholder="What's this about?"
                    />
                  </div>

                  <div>
                    <label
                      htmlFor="message"
                      className="block text-sm font-medium text-locasa-text-light dark:text-locasa-text-dark mb-2"
                    >
                      Message
                    </label>
                    <textarea
                      id="message"
                      name="message"
                      value={formData.message}
                      onChange={handleInputChange}
                      required
                      rows={6}
                      className="w-full px-4 py-3 rounded-lg border border-locasa-border-light/30 dark:border-locasa-border-dark/30 bg-locasa-input-light dark:bg-locasa-input-dark text-locasa-text-light dark:text-locasa-text-dark placeholder-locasa-secondary-light dark:placeholder-locasa-secondary-dark focus:outline-none focus:ring-2 focus:ring-locasa-primary-light dark:focus:ring-locasa-primary-dark resize-none"
                      placeholder="Tell us more about your inquiry..."
                    />
                  </div>

                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className={cn(
                      "w-full flex items-center justify-center gap-2 px-6 py-3 rounded-lg font-medium text-locasa-background-light dark:text-white",
                      "bg-locasa-primary-light dark:bg-locasa-primary-dark",
                      "hover:bg-locasa-primary-light/90 dark:hover:bg-locasa-primary-dark/90",
                      "focus:outline-none focus:ring-2 focus:ring-locasa-primary-light dark:focus:ring-locasa-primary-dark",
                      "transition-colors duration-200",
                      "disabled:opacity-50 disabled:cursor-not-allowed"
                    )}
                  >
                    {isSubmitting ? (
                      <>
                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                        Sending...
                      </>
                    ) : (
                      <>
                        <Send className="h-4 w-4" />
                        Send Message
                      </>
                    )}
                  </button>
                </form>
              </div>
            </div>

            {/* FAQ Section */}
            <div>
              <div className="mb-8">
                <div className="flex items-center gap-3 mb-6">
                  <Clock className="h-6 w-6 text-locasa-primary-light dark:text-locasa-primary-dark" />
                  <h2 className="text-2xl font-bold text-locasa-text-light dark:text-locasa-text-dark">
                    Frequently Asked Questions
                  </h2>
                </div>
                <p className="text-locasa-secondary-light dark:text-locasa-secondary-dark">
                  Quick answers to common questions about Locasa
                </p>
              </div>

              <div className="space-y-4">
                {faqs.map((faq, index) => (
                  <div
                    key={index}
                    className="bg-locasa-background-light dark:bg-locasa-background-dark rounded-xl p-6 shadow-sm"
                  >
                    <h3 className="text-lg font-semibold text-locasa-text-light dark:text-locasa-text-dark mb-3">
                      {faq.question}
                    </h3>
                    <p className="text-locasa-secondary-light dark:text-locasa-secondary-dark">
                      {faq.answer}
                    </p>
                  </div>
                ))}
              </div>

              <div className="mt-8 p-6 bg-locasa-primary-light/10 dark:bg-locasa-primary-dark/10 rounded-xl border border-locasa-primary-light/20 dark:border-locasa-primary-dark/20">
                <h3 className="text-lg font-semibold text-locasa-text-light dark:text-locasa-text-dark mb-2">
                  Still have questions?
                </h3>
                <p className="text-locasa-secondary-light dark:text-locasa-secondary-dark mb-4">
                  Can't find the answer you're looking for? Please chat with our
                  friendly team.
                </p>
                <button className="inline-flex items-center gap-2 px-4 py-2 bg-locasa-primary-light dark:bg-locasa-primary-dark text-locasa-background-light dark:text-white rounded-lg hover:bg-locasa-primary-light/90 dark:hover:bg-locasa-primary-dark/90 transition-colors font-medium">
                  <MessageCircle className="h-4 w-4" />
                  Start a Chat
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
