"use client";

import React from "react";
import {
  Shield,
  Eye,
  Lock,
  Users,
  Database,
  Mail,
  Calendar,
} from "lucide-react";
import { cn } from "@/lib/utils";

export default function PrivacyPolicy() {
  const sections = [
    {
      id: "information-collection",
      title: "Information We Collect",
      icon: Database,
      content: [
        "Personal information you provide when creating an account (name, email, phone number)",
        "Business information for vendor accounts (business name, address, contact details)",
        "Transaction data when you make purchases or sales through our platform",
        "Usage data including how you interact with our services",
        "Device information such as IP address, browser type, and operating system",
        "Location data when you use location-based features (with your permission)",
      ],
    },
    {
      id: "information-use",
      title: "How We Use Your Information",
      icon: Eye,
      content: [
        "To provide and maintain our marketplace services",
        "To process transactions and send related notifications",
        "To communicate with you about your account and our services",
        "To improve our platform and develop new features",
        "To ensure security and prevent fraud",
        "To comply with legal obligations and resolve disputes",
      ],
    },
    {
      id: "information-sharing",
      title: "Information Sharing",
      icon: Users,
      content: [
        "We do not sell your personal information to third parties",
        "We may share information with service providers who help us operate our platform",
        "Business information may be visible to customers browsing our marketplace",
        "We may disclose information when required by law or to protect our rights",
        "In case of business transfer, your information may be transferred to new owners",
      ],
    },
    {
      id: "data-security",
      title: "Data Security",
      icon: Lock,
      content: [
        "We use industry-standard encryption to protect your data",
        "Regular security audits and monitoring systems are in place",
        "Access to personal information is restricted to authorized personnel only",
        "We maintain secure servers and follow best practices for data protection",
        "Payment information is processed through secure, PCI-compliant systems",
      ],
    },
  ];

  return (
    <div className="min-h-screen bg-locasa-background-light dark:bg-locasa-background-dark">
      {/* Hero Section */}
      <section className="py-20 lg:py-32 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-locasa-primary-light/5 to-locasa-success-light/5 dark:from-locasa-primary-dark/5 dark:to-locasa-success-dark/5"></div>

        <div className="relative max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-locasa-success-light/10 dark:bg-locasa-success-dark/10 text-locasa-success-light dark:text-locasa-success-dark text-sm font-medium mb-6">
            <Shield className="h-4 w-4" />
            Your Privacy Matters
          </div>

          <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-locasa-text-light dark:text-locasa-text-dark mb-6 leading-tight">
            Privacy Policy
          </h1>

          <p className="text-lg sm:text-xl text-locasa-secondary-light dark:text-locasa-secondary-dark mb-8 max-w-3xl mx-auto">
            We are committed to protecting your privacy and ensuring the
            security of your personal information. This policy explains how we
            collect, use, and safeguard your data.
          </p>

          <div className="flex items-center justify-center gap-2 text-sm text-locasa-secondary-light dark:text-locasa-secondary-dark">
            <Calendar className="h-4 w-4" />
            <span>Last updated: December 2024</span>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="py-16 lg:py-20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Introduction */}
          <div className="mb-12 p-6 bg-locasa-input-light/30 dark:bg-locasa-input-dark/30 rounded-2xl border border-locasa-border-light/20 dark:border-locasa-border-dark/20">
            <h2 className="text-2xl font-bold text-locasa-text-light dark:text-locasa-text-dark mb-4">
              Introduction
            </h2>
            <p className="text-locasa-secondary-light dark:text-locasa-secondary-dark leading-relaxed">
              Locasa ("we," "our," or "us") operates the Locasa marketplace
              platform. This Privacy Policy informs you of our policies
              regarding the collection, use, and disclosure of personal data
              when you use our service and the choices you have associated with
              that data.
            </p>
          </div>

          {/* Privacy Sections */}
          <div className="space-y-8">
            {sections.map((section, index) => {
              const Icon = section.icon;
              return (
                <div
                  key={section.id}
                  className={cn(
                    "p-8 rounded-2xl border border-locasa-border-light/20 dark:border-locasa-border-dark/20",
                    "bg-locasa-input-light/30 dark:bg-locasa-input-dark/30 backdrop-blur-sm"
                  )}
                >
                  <div className="flex items-center gap-4 mb-6">
                    <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-locasa-primary-light to-locasa-success-light dark:from-locasa-primary-dark dark:to-locasa-success-dark flex items-center justify-center">
                      <Icon className="h-6 w-6 text-locasa-background-light dark:text-white" />
                    </div>
                    <h3 className="text-2xl font-bold text-locasa-text-light dark:text-locasa-text-dark">
                      {section.title}
                    </h3>
                  </div>

                  <ul className="space-y-3">
                    {section.content.map((item, itemIndex) => (
                      <li key={itemIndex} className="flex items-start gap-3">
                        <div className="w-2 h-2 rounded-full bg-locasa-primary-light dark:bg-locasa-primary-dark mt-2 flex-shrink-0"></div>
                        <span className="text-locasa-secondary-light dark:text-locasa-secondary-dark leading-relaxed">
                          {item}
                        </span>
                      </li>
                    ))}
                  </ul>
                </div>
              );
            })}
          </div>

          {/* Your Rights Section */}
          <div className="mt-12 p-8 bg-gradient-to-br from-locasa-primary-light/10 to-locasa-success-light/10 dark:from-locasa-primary-dark/10 dark:to-locasa-success-dark/10 rounded-2xl border border-locasa-border-light/20 dark:border-locasa-border-dark/20">
            <h3 className="text-2xl font-bold text-locasa-text-light dark:text-locasa-text-dark mb-6">
              Your Rights
            </h3>
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-semibold text-locasa-text-light dark:text-locasa-text-dark mb-2">
                  Access & Portability
                </h4>
                <p className="text-locasa-secondary-light dark:text-locasa-secondary-dark text-sm">
                  You have the right to access and receive a copy of your
                  personal data.
                </p>
              </div>
              <div>
                <h4 className="font-semibold text-locasa-text-light dark:text-locasa-text-dark mb-2">
                  Correction
                </h4>
                <p className="text-locasa-secondary-light dark:text-locasa-secondary-dark text-sm">
                  You can update or correct your personal information at any
                  time.
                </p>
              </div>
              <div>
                <h4 className="font-semibold text-locasa-text-light dark:text-locasa-text-dark mb-2">
                  Deletion
                </h4>
                <p className="text-locasa-secondary-light dark:text-locasa-secondary-dark text-sm">
                  You can request deletion of your personal data, subject to
                  legal requirements.
                </p>
              </div>
              <div>
                <h4 className="font-semibold text-locasa-text-light dark:text-locasa-text-dark mb-2">
                  Opt-out
                </h4>
                <p className="text-locasa-secondary-light dark:text-locasa-secondary-dark text-sm">
                  You can opt-out of marketing communications and certain data
                  processing.
                </p>
              </div>
            </div>
          </div>

          {/* Contact Section */}
          <div className="mt-12 text-center p-8 bg-locasa-input-light/30 dark:bg-locasa-input-dark/30 rounded-2xl border border-locasa-border-light/20 dark:border-locasa-border-dark/20">
            <Mail className="h-12 w-12 text-locasa-primary-light dark:text-locasa-primary-dark mx-auto mb-4" />
            <h3 className="text-2xl font-bold text-locasa-text-light dark:text-locasa-text-dark mb-4">
              Questions About Privacy?
            </h3>
            <p className="text-locasa-secondary-light dark:text-locasa-secondary-dark mb-6">
              If you have any questions about this Privacy Policy or our data
              practices, please don't hesitate to contact us.
            </p>
            <a
              href="mailto:<EMAIL>"
              className="inline-flex items-center gap-2 px-6 py-3 bg-locasa-primary-light dark:bg-locasa-primary-dark text-locasa-background-light dark:text-white rounded-lg hover:bg-locasa-primary-light/90 dark:hover:bg-locasa-primary-dark/90 transition-colors font-medium"
            >
              <Mail className="h-4 w-4" />
              <EMAIL>
            </a>
          </div>
        </div>
      </section>
    </div>
  );
}
