/**
 * Below are the colors that are used in the app. The colors are defined in the light and dark mode.
 */

type ColorScheme = {
  disabled: string;
  text: string;
  background: string;
  primary: string;
  secondary: string;
  thirdary: string;
  error: string;
  success: string;
  warning: string;
  info: string;
  green: string;
  orange: string;
  yellow: string;
  blue: string;
  gray: string;
  white: string;
  black: string;
  input: string;
  input_text: string;
  border: string;
  star: string;
  no_star: string;
  tab: string;
  inactive_tab: string;
};

export type AppTheme = "light" | "dark";
export type AppThemeColors = Record<AppTheme, ColorScheme>;

export const Colors: AppThemeColors = {
  light: {
    disabled: "rgba(0, 0, 0, 0.15)",
    text: "#1C1C1C",
    background: "#F7F5FA",
    primary: "#2C3930",
    secondary: "#555",
    thirdary: "#DCD7C9",
    error: "#E64848",
    success: "#2AA876",
    warning: "#F4A100",
    info: "#2680EB",
    green: "#198754",
    orange: "#EC6C42",
    yellow: "#F5B700",
    blue: "#2196F3",
    gray: "#888",
    white: "#F7F5FA",
    black: "#1C1C1C",
    input: "#DCD7C9",
    input_text: "#3F4F44",
    border: "#555",
    star: "#3F4F44",
    no_star: "#DCD7C9",
    tab: "#1C1C1C",
    inactive_tab: "#2b9",
  },

  dark: {
    disabled: "rgba(255, 255, 255, 0.2)",
    text: "#F1F1F1",
    background: "#0E0E0E",
    primary: "#D0E7D2",
    secondary: "#CCCCCC",
    thirdary: "#DCD7C9",
    error: "#F28B82",
    success: "#81C995",
    warning: "#FFC107",
    info: "#82B1FF",
    green: "#4CAF50",
    orange: "#FF8A65",
    yellow: "#FFD54F",
    blue: "#64B5F6",
    gray: "#AAAAAA",
    white: "#F1F1F1",
    black: "#1C1C1C",
    input: "#2E2E2E",
    input_text: "#E0E0E0",
    border: "#3A3A3A",
    star: "#F5C518",
    no_star: "#555555",
    tab: "#F1F1F1",
    inactive_tab: "#2AA876",
  },
};
