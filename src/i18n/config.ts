import { notFound } from "next/navigation";
import { getRequestConfig } from "next-intl/server";
import { getUserLocale } from "@/lib/locale";

// Can be imported from a shared config
export const locales = ["en", "fr"] as const;
export type Locale = (typeof locales)[number];

export default getRequestConfig(async () => {
  // Get the locale from cookies
  const locale = await getUserLocale();

  // Validate that the locale is valid
  if (!locales.includes(locale)) notFound();

  return {
    messages: (await import(`./messages/${locale}.json`)).default,
    locale: locale,
  };
});
