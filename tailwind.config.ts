import type { Config } from "tailwindcss";

const config: Config = {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  darkMode: "class",
  theme: {
    extend: {
      colors: {
        // Locasa brand colors
        locasa: {
          primary: {
            light: "#2C3930",
            dark: "#D0E7D2",
          },
          secondary: {
            light: "#555",
            dark: "#CCCCCC",
          },
          background: {
            light: "#F7F5FA",
            dark: "#0E0E0E",
          },
          text: {
            light: "#1C1C1C",
            dark: "#F1F1F1",
          },
          success: {
            light: "#2AA876",
            dark: "#81C995",
          },
          error: {
            light: "#E64848",
            dark: "#F28B82",
          },
          warning: {
            light: "#F4A100",
            dark: "#FFC107",
          },
          info: {
            light: "#2680EB",
            dark: "#82B1FF",
          },
          input: {
            light: "#DCD7C9",
            dark: "#2E2E2E",
          },
          border: {
            light: "#555",
            dark: "#3A3A3A",
          },
        },
      },
      fontFamily: {
        sans: ["Inter", "system-ui", "sans-serif"],
      },
      animation: {
        "fade-in": "fadeIn 0.5s ease-in-out",
        "slide-up": "slideUp 0.3s ease-out",
        "slide-down": "slideDown 0.3s ease-out",
      },
      keyframes: {
        fadeIn: {
          "0%": { opacity: "0" },
          "100%": { opacity: "1" },
        },
        slideUp: {
          "0%": { transform: "translateY(10px)", opacity: "0" },
          "100%": { transform: "translateY(0)", opacity: "1" },
        },
        slideDown: {
          "0%": { transform: "translateY(-10px)", opacity: "0" },
          "100%": { transform: "translateY(0)", opacity: "1" },
        },
      },
    },
  },
  plugins: [],
};

export default config;
