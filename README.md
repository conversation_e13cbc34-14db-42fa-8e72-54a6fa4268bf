# Locasa Website

A modern, responsive marketplace website for local brands and vendors built with Next.js, TypeScript, and Tailwind CSS.

## Features

- 🌍 **Internationalization**: Support for English and French languages
- 🌙 **Dark/Light Mode**: Seamless theme switching with system preference detection
- 📱 **Responsive Design**: Optimized for all device sizes
- 🔐 **Authentication**: Complete auth system for vendors and clients
- 🛒 **Marketplace**: Browse and discover local products and services
- 👥 **User Dashboards**: Separate dashboards for vendors and clients
- 🎨 **Modern UI**: Beautiful, accessible components with Tailwind CSS
- ⚡ **Performance**: Built with Next.js 15 and React Query for optimal performance

## Tech Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS with custom animations
- **Internationalization**: next-intl
- **Icons**: Lucide React
- **Theme**: Custom dark/light mode with beautiful color scheme

## Getting Started

1. **Clone the repository**

   ```bash
   git clone <repository-url>
   cd locasa-website
   ```

2. **Install dependencies**

   ```bash
   npm install
   ```

3. **Run the development server**

   ```bash
   npm run dev
   ```

4. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## Project Structure

```
src/
├── app/                    # Next.js App Router pages
│   ├── [locale]/          # Internationalized routes
│   │   ├── auth/          # Authentication pages
│   │   ├── marketplace/   # Marketplace pages
│   │   ├── vendor/        # Vendor dashboard
│   │   └── client/        # Client dashboard
├── components/            # Reusable UI components
│   ├── auth/             # Authentication components
│   ├── landing/          # Landing page components
│   ├── navigation/       # Navigation components
│   └── ui/               # Base UI components
├── contexts/             # React contexts
├── hooks/                # Custom React hooks
├── i18n/                 # Internationalization
├── lib/                  # Utility libraries
└── services/             # API services
```

## Available Pages

- **Landing Page** (`/`): Hero section with app showcase
- **Marketplace** (`/marketplace`): Browse local products and services
- **Authentication** (`/auth/login`, `/auth/signup`): User authentication
- **Vendor Dashboard** (`/vendor/dashboard`): Vendor management interface
- **Client Dashboard** (`/client/dashboard`): Client account management
- **About** (`/about`): Company information and values
- **Contact** (`/contact`): Contact form and information

## User Types

### Vendors

- Create and manage product listings
- View orders and analytics
- Manage inventory
- Access vendor-specific tools

### Clients

- Browse marketplace
- Make purchases
- Manage orders and favorites
- Access client dashboard

## Internationalization

The website supports multiple languages:

- English (default)
- French

Language switching is available in the navigation bar.

## Theme System

- **Light Mode**: Clean, bright interface
- **Dark Mode**: Easy on the eyes for low-light environments
- **System Preference**: Automatically detects user's system preference
- **Manual Toggle**: Users can manually switch themes

## API Integration

The website is designed to work with a backend API for:

- User authentication
- Product management
- Order processing
- User profiles

API endpoints are configured in `src/services/api.ts`.

## Development

### Adding New Pages

1. Create a new page in `src/app/[locale]/`
2. Add translations in `src/i18n/messages/`
3. Update navigation if needed

### Adding New Components

1. Create component in appropriate `src/components/` subdirectory
2. Export from index file if needed
3. Add proper TypeScript types

### Styling Guidelines

- Use Tailwind CSS utility classes
- Follow the design system defined in `tailwind.config.ts`
- Use the custom color palette for consistency

## Build and Deploy

```bash
# Build for production
npm run build

# Start production server
npm start

# Lint code
npm run lint
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License.
